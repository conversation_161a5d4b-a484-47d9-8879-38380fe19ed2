<!--
title: '[QBO-Document-Service] Serverless Nodejs Rest API with TypeScript'
description: 'This is REST API for AWS Lambda By Serverless framwork with TypeScript.'
layout: Doc
framework: v1
platform: AWS
language: nodeJS
priority: 10
authorName: 'AmagumoLabs'
-->
# [QBO-Document-Service] Serverless Nodejs Rest API with TypeScript

This is REST API for AWS Lambda By Serverless framwork with TypeScript.

#### Node v20.14.0
#### Serverless Version 4

## Local Development

### Run locally (some AWS services might not work properly)
```bash
npm run local

# or

serverless offline
```

### Run Dev (Hybrid option - Locally but on Cloud)
The easiest way to develop and test your function is to use the `dev` command:
```bash
npm run dev

# or

serverless dev
```
This will start a local emulator of AWS Lambda and tunnel your requests to and from AWS Lambda, allowing you to interact with your function as if it were running in the cloud.

Now you can invoke the function as before, but this time the function will be executed locally. Now you can develop your function locally, invoke it, and see the results immediately without having to re-deploy.

When you are done developing, don't forget to run `serverless deploy` to deploy the function to the cloud.


```bash
serverless invoke local --function <function_name>
```

Which should result in:

```bash
{
    "statusCode": 200,
    "body": ""
}
```

Logs watch
```bash
$ serverless logs -f myFunction
```

### Migrate database

```bash
npm run migrate
npm run seed
```

Create new table
```bash
cd src && npx sequelize-cli migration:create --name create_user_table
```

Create new seed
```bash
cd src && npx sequelize-cli seed:generate --name demo-user
```

### Unit test

#### Migrate database

```bash
npm run migrate:unit-test
npm run seed:unit-test
```

#### Run unit test

```bash
npm run test
```

Run unit test in debug mode

```bash
npm run unit-debug
```

### Deploy

Deploy one function
```bash
$ serverless deploy function -f myFunction

# or

$ npm run deploy:function -- -f myFunction
```

### Deploy on AWS, simply run:

```
$ npm run deploy

# or

$ serverless deploy
```

### Force to update layers

```bash
npm install -D database-layer@bitbucket:ecodropnet/qbodatabaselayerv2.git#Feature-Test-Dev
npm install -D middy-layer@bitbucket:ecodropnet/qbomiddylayerv2.git#Feature-Test-Dev
npm install -D utils-layer@bitbucket:ecodropnet/qboutilslayerv2.git#Feature-Test-Dev
```