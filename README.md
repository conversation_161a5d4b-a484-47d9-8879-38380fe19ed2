<!--
title: '[QBO-Document-Service] Serverless Nodejs Rest API with TypeScript'
description: 'This is REST API for AWS Lambda By Serverless framwork with TypeScript.'
layout: Doc
framework: v1
platform: AWS
language: nodeJS
priority: 10
authorName: 'AmagumoLabs'
-->
# QBO-Document-Service

[![Node.js](https://img.shields.io/badge/node-20.14.0-brightgreen)](https://nodejs.org/)
[![Serverless](https://img.shields.io/badge/serverless-v4-blue)](https://www.serverless.com/)

> **Serverless Node.js REST API for AWS Lambda, written in TypeScript**

---

## Table of Contents
- [Features](#features)
- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Project Structure](#project-structure)
- [Usage](#usage)
  - [Local Development](#local-development)
  - [Hybrid Cloud Development](#hybrid-cloud-development)
  - [Database Management](#database-management)
  - [Testing](#testing)
  - [Deployment](#deployment)
- [Contributing](#contributing)
- [License](#license)

---

## Features
- Serverless REST API using AWS Lambda
- Written in TypeScript
- Uses Serverless Framework v4
- Sequelize ORM for database migrations and seeding
- Unit and integration testing with Jest

---

## Prerequisites
- [Node.js v20.14.0+](https://nodejs.org/)
- [npm](https://www.npmjs.com/)
- [Serverless Framework v4](https://www.serverless.com/)
- AWS account and credentials (for cloud deployment)

---

## Installation

Clone the repository and install dependencies:

```bash
npm install
```

---

## Project Structure

- `src/` - Source code (handlers, models, services, etc.)
- `src/migrations/` - Sequelize migrations
- `src/seeders/` - Sequelize seeders
- `src/tests/` - Unit and feature tests
- `serverless.yml` - Serverless Framework configuration
- `templates/` - Serverless custom templates

---

## Usage

### Local Development

Run the service locally (some AWS services may not be fully emulated):

```bash
npm run local
# or
serverless offline
```

#### Hybrid Cloud Development (Dev Mode)

Tunnel requests to AWS Lambda for realistic cloud testing:

```bash
npm run dev
# or
serverless dev
```

This starts a local emulator and tunnels requests to AWS Lambda, allowing you to develop and test as if running in the cloud.

#### Invoke a Function Locally

```bash
serverless invoke local --function <function_name>
```

#### View Logs

```bash
serverless logs -f <function_name>
```

---

### Database Management

#### Run Migrations & Seeds

```bash
npm run migrate      # Run all migrations
npm run seed         # Run all seeders
```

#### Create a New Migration or Seed

```bash
npm run migrate:create -- <migration-name>
npm run seed:create -- <seed-name>
```

#### Rollback Migrations or Seeds

```bash
npm run migrate:rollback
npm run seed:rollback
```

#### Unit Test Database

```bash
npm run migrate:unit-test
npm run seed:unit-test
```

---

### Testing

#### Run All Unit Tests

```bash
npm run test
```

#### Run Unit Tests in Debug Mode

```bash
npm run unit-debug
```

---

### Deployment

#### Deploy a Single Function

```bash
serverless deploy function -f <function_name>
# or
npm run deploy:function -- -f <function_name>
```

#### Deploy Entire Service to AWS

```bash
npm run deploy
# or
serverless deploy
```

---

### Update Layers (Force)

```bash
npm install -D database-layer@bitbucket:ecodropnet/qbodatabaselayerv2.git#Feature-Test-Dev
npm install -D middy-layer@bitbucket:ecodropnet/qbomiddylayerv2.git#Feature-Test-Dev
npm install -D utils-layer@bitbucket:ecodropnet/qboutilslayerv2.git#Feature-Test-Dev
```