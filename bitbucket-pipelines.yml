definitions:
  caches:
    node: node_modules

image: node:20.14.0
pipelines:
  custom:
    deploy-uat:
      - stage:
          name: Build and Deploy to UAT
          deployment: uat
          steps:
            - step:
                oidc: true
                name: Unit Test
                cache:
                  - node
                script:
                  - npm install
                  - npm install -D database-layer@bitbucket:ecodropnet/qbodatabaselayerv2.git#Feature-Test-UAT
                  - npm install -D middy-layer@bitbucket:ecodropnet/qbomiddylayerv2.git#Feature-Test-UAT
                  - npm install -D utils-layer@bitbucket:ecodropnet/qboutilslayerv2.git#Feature-Test-UAT
                  - npm run migrate:unit-test
                  - npm run seed:unit-test
                  - npm run test
            - step:
                oidc: true
                name: Deploy to UAT
                cache:
                  - node
                runs-on:
                  - qbo.uat
                  - self.hosted
                  - linux
                script:
                  - export NODE_ENV=uat
                  - export CI=true
                  - npm ci --only=production
                  - npm install -g serverless cross-env sequelize-cli sequelize mysql2 pg-hstore
                  - npm run migrate
                  - npm run seed
                  - serverless deploy --stage uat --region ${REGION}
    deploy-prod:
      - stage:
          name: Build and Deploy to Production
          deployment: production
          steps:
            - step:
                oidc: true
                name: Deploy to Production
                runs-on:
                  - qbo.prod
                  - self.hosted
                  - linux
                script:
                  - export NODE_ENV=production
                  - export CI=true
                  - npm ci
                  - npm install -g serverless cross-env sequelize-cli sequelize mysql2 pg-hstore
                  - npm run migrate
                  - npm run seed
                  - serverless deploy --stage prod --region ${REGION}
                  - export VERSION="v$(node -p "require('./package.json').version")"
                  - git tag -a ${VERSION} -m "Development deployment v${VERSION}"
                  - git push origin ${VERSION}
  branches:
    Feature-Test-Dev:
      - stage:
          name: Build and Deploy to Development
          deployment: development
          steps:
            - step:
                oidc: true
                name: Unit Test
                script:
                  - npm install
                  - npm install database-layer middy-layer utils-layer --save-dev
                  - npm run migrate:unit-test
                  - npm run seed:unit-test
                  - npm run test
            - step:
                oidc: true
                name: Deploy to DEV
                script:
                  - npm install
                  - npm install -g serverless
                  - npm run migrate
                  - npm run seed
                  - serverless deploy --stage dev --region ${REGION}