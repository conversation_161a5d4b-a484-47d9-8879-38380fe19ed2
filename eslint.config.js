/* eslint-disable @typescript-eslint/no-require-imports */
const globals = require('globals');
const pluginJs = require('@eslint/js');
const tseslint = require('typescript-eslint');

module.exports = [
  { files: ['**/*.{ts}'] },
  { languageOptions: { globals: globals.node } },
  pluginJs.configs.recommended,
  ...tseslint.configs.recommended,
  { ignores: ['node_modules/*', '.serverless/*', 'coverage/*', '.prettierrc.js', 'jest.global.setup.ts', '**/src/tests/**',] },
  {
    rules: {
      ...tseslint.configs.strictTypeChecked.rules,
      semi: ['error', 'always'],
      quotes: [1, 'single', { avoidEscape: true }],
    },
  },
];
