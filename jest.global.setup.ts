import 'dotenv/config';
import { QueryTypes, sequelize } from 'database-layer';
// import fixtures from 'sequelize-fixtures';

// const models = {};

export async function truncateDatabaseExcept(excludedTables: string[] = []) {
  try {
    // Get a list of all table names in the database
    const query = 'SELECT table_name FROM information_schema.tables WHERE table_schema = :databaseName';
    const tableNames = await sequelize.query(query, {
      type: QueryTypes.SELECT,
      raw: true,
      replacements: { databaseName: sequelize.config.database },
    });
    // Filter out the excluded tables
    const tablesToTruncate = tableNames
      .map((row: any) => row.TABLE_NAME)
      .filter((tableName) => !excludedTables.includes(tableName));

    // Truncate the selected tables
    for (const tableName of tablesToTruncate) {
      await sequelize
        .query('SET FOREIGN_KEY_CHECKS = 0')
        .then(function () {
          return sequelize.query(`TRUNCATE TABLE ${tableName}`, {
            type: QueryTypes.RAW,
          });
        })
        .then(function () {
          return sequelize.query('SET FOREIGN_KEY_CHECKS = 1');
        });
      console.log(`Table ${tableName} truncated.`);
    }

    console.log('Database truncation completed.');
  } catch (error) {
    console.error('Error truncating database:', error);
  }
}

export default async () => {
  try {
    // Define tables to exclude from truncation
    const excludedTables = ['SequelizeData', 'SequelizeMeta', 'document_types', 'document_statuses'];
    // Run the truncateDatabaseExcept function
    // Should enable this option to truncate all tables except the specified ones
    await truncateDatabaseExcept(excludedTables);
    console.log('Database reset successfully');
    // await fixtures.loadFiles([], models);
    console.log('Database init successfully');
  } catch (error) {
    console.error('Error resetting database:', error);
    await sequelize.close();
  } finally {
    // Close the database connection
    await sequelize.close();
  }
};
