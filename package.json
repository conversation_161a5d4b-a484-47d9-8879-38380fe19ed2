{"name": "qbodocumentservice", "version": "1.02.02", "description": "QBO Document Service", "scripts": {"unit": "cross-env NODE_ENV=test && jest --silent --runInBand --logHeapUsage", "unit-debug": "cross-env NODE_ENV=test && jest --runInBand --logHeapUsage", "test": "cross-env NODE_OPTIONS=--max_old_space_size=4096 && cross-env NODE_ENV=test npm run unit", "dev": "serverless dev", "local": "serverless offline start --reload<PERSON><PERSON><PERSON>", "deploy": "serverless deploy", "lint": "eslint . --fix", "migrate": "cd src && cross-env NODE_ENV=development sequelize-cli db:migrate --env development && cd ..", "migrate:create": "cd src && cross-env NODE_ENV=development sequelize-cli migration:create --name", "migrate:rollback": "cd src && cross-env NODE_ENV=development sequelize-cli db:migrate:undo --env development && cd ..", "migrate:unit-test": "cd src && cross-env NODE_ENV=test sequelize-cli db:migrate --env test && cd ..", "seed": "cd src && cross-env NODE_ENV=development sequelize-cli db:seed:all --env development && cd ..", "seed:create": "cd src && cross-env NODE_ENV=development sequelize-cli seed:create --name", "seed:rollback": "cd src && cross-env NODE_ENV=development sequelize-cli db:seed:undo --env development && cd ..", "seed:unit-test": "cd src && cross-env NODE_ENV=test sequelize-cli db:seed:all --env test && cd ..", "format": "prettier --write \"src/**/*.ts\" \"src/tests/**/*.ts\"", "lint-staged": "lint-staged", "prepare": "husky"}, "lint-staged": {"*.ts": ["npm run lint", "npm run format", "git add ."]}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "ISC", "devDependencies": {"@aws-sdk/client-eventbridge": "^3.651.1", "@aws-sdk/client-lambda": "^3.804.0", "@aws-sdk/client-s3": "^3.688.0", "@aws-sdk/client-sns": "^3.682.0", "@aws-sdk/s3-request-presigner": "^3.688.0", "@eslint/js": "^9.9.1", "@faker-js/faker": "^8.4.1", "@types/aws-lambda": "^8.10.145", "@types/cls-hooked": "^4.3.8", "@types/jest": "^29.5.12", "@types/node": "^22.5.1", "@types/sequelize-fixtures": "^0.6.6", "cross-env": "^7.0.3", "database-layer": "bitbucket:ecodropnet/qbodatabaselayerv2#Feature-Test-Dev", "eslint": "^9.9.1", "globals": "^15.9.0", "husky": "^9.1.5", "jest": "^29.7.0", "lint-staged": "^15.2.9", "middy-layer": "bitbucket:ecodropnet/qbomiddylayerv2#Feature-Test-Dev", "pg-hstore": "^2.3.4", "prettier": "^3.3.3", "sequelize": "^6.35.1", "sequelize-cli": "^6.6.2", "sequelize-fixtures": "^1.2.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript-eslint": "^8.4.0", "utils-layer": "bitbucket:ecodropnet/qboutilslayerv2#Feature-Test-Dev"}, "dependencies": {"axios": "^1.7.4", "dayjs": "^1.11.13", "dotenv": "^16.4.5", "lambda-multipart-parser": "^1.0.1", "probe-image-size": "^7.2.3", "serverless-iam-roles-per-function": "^3.2.0", "serverless-offline": "^14.2.0", "serverless-step-functions": "^3.21.2", "uuid": "^11.0.2"}}