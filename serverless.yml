app: quotation-back-office
# "service" is the name of this project. This will also be added to your AWS resource names.
service: qbodocumentservice

build:
  esbuild:
    external:
      - pg-hstore
      - database-layer
      - middy-layer
      - utils-layer
package:
  individually: true

provider:
  name: aws
  runtime: nodejs20.x
  stage: ${opt:stage, 'local'}
  endpointType: REGIONAL
  region: ${opt:region, "ap-southeast-1"}
  stackTags: ${self:custom.tags}
  vpc: ${self:custom.vpc.${self:provider.stage}, ''}
  apiGateway:
    timeoutInMillis: 120000
  tracing:
    apiGateway: true
    lambda: true
  layers:
    - ${self:custom.databaseLayer}
    - ${self:custom.middyLayer}
    - ${self:custom.utilsLayer}
  environment:
    STAGE: ${self:provider.stage}
    REGION: ${self:provider.region}
    SERVICE_NAME: ${self:service}
    ZOHO_SERVICE_NAME: ${self:custom.zohoService}
    DB_HOST: ${env:DB_HOST}
    DB_USERNAME: ${env:DB_USERNAME}
    DB_PASSWORD: ${env:DB_PASSWORD}
    DB_DATABASE: ${env:DB_DATABASE}
    S3_BUCKET_PATH: ${env:S3_BUCKET_PATH}
    GOOGLE_MAPS_API_KEY: ${env:GOOGLE_MAPS_API_KEY}
    EVENT_BUS_NAME: ${self:custom.eventBusName}
    ZOHO_REQUEST_TOPIC_ARN: ${self:custom.zohoRequestsTopic}
    SYNC_DOCUMENT_TO_ZOHO: ${self:custom.stepFunctions.SyncDocumentToZoho}
    SYNC_CONTACT_ADDRESS_FROM_ZOHO: ${self:custom.stepFunctions.SyncContactAddressFromZoho}
    DATABASE_CHANGE_TOPIC_ARN: ${self:custom.databaseChangeTopic}

custom: ${file(templates/custom.yml):custom}

functions:
  - ${file(templates/functions.yml)}
  - ${file(templates/functions/document.yml)}
  - ${file(templates/functions/zoho-events.yml)}
  - ${file(templates/functions/document-type.yml)}
  - ${file(templates/functions/document-status.yml)}
  - ${file(templates/functions/document-cc-contact.yml)}
  - ${file(templates/functions/document-file-upload.yml)}
  - ${file(templates/functions/document-cc-libres-contact.yml)}
  - ${file(templates/functions/document-product-line-price.yml)}
  - ${file(templates/functions/document-product-line-sub-option.yml)}
  - ${file(templates/functions/document-product-line-prestation.yml)}
  - ${file(templates/functions/document-product-line-prestation-status.yml)}

stepFunctions:
  stateMachines:
    CreateOrUpdateDocument: ${file(templates/functions/document/create-or-update-document-sf.yml)}

plugins: ${file(templates/plugins.yml):plugins}

resources: ${file(templates/resources.yml)}
