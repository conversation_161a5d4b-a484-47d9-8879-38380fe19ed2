import { BulkCreateOptions, InferAttributes } from 'database-layer';
import {
  DocumentProductLine,
  DocumentProductLinePrestation,
  DocumentProductLinePrestationSubOption,
  DocumentProductLinePrice,
  DocumentProductLineSubOption,
} from '../models';
export const STAGE = process.env.STAGE;
export const REGION = process.env.REGION;
export const SERVICE_NAME = process.env.SERVICE_NAME;
export const ZOHO_SERVICE_NAME = process.env.ZOHO_SERVICE_NAME;
export const ZOHO_REQUEST_TOPIC_ARN = process.env.ZOHO_REQUEST_TOPIC_ARN;
export const SYNC_DOCUMENT_TO_ZOHO = process.env.SYNC_DOCUMENT_TO_ZOHO;
export const SYNC_CONTACT_ADDRESS_FROM_ZOHO = process.env.SYNC_CONTACT_ADDRESS_FROM_ZOHO;
export const ZOHO_GROUP_ID = 'ZOHO_GROUP_ID';
export const ORIGINAL_PATH = 'originals';
export const FILE_PATH = process.env.S3_BUCKET_PATH;
export const IMAGE_UPLOAD_BUCKET = FILE_PATH.split('.s3.')[0].split('//')[1] || '';
export const S3_REGION = FILE_PATH.match(/\.s3\.([^.]+)\.amazonaws\.com/)?.[1] || '';
export const NODE_ENV = {
  TEST: 'test',
  DEVELOPMENT: 'development',
};
export const DOCUMENT_TYPES = {
  QUOTATION: 'quotation',
  ORDER: 'order',
  INVOICE: 'invoice',
};

export const DOCUMENT_UPDATE_FIELDS = {
  CREATE_DOCUMENT_EXTRA_DATA: [
    'crmContactId',
    'customerId',
    'contactPersons',
    'salespersonId',
    'salespersonPhone',
    'booksContactId',
    'lastModifyById',
    'lastModifyName',
    'lastModifyPhone',
    'lastModifyEmail',
    'siteAddress',
    'billingAddress',
    'zohoApiClientId',
    'zohoApiConnection',
    'booksCurrentSubStatusId',
    'booksCurrentSubStatus',
    'estimateNumber',
    'contact',
    'contactPerson',
    'vendeurCrm',
  ],
  UPDATE_DOCUMENT_EXTRA_DATA: [
    'crmContactId',
    'customerId',
    'contactPersons',
    'salespersonId',
    'salespersonPhone',
    'booksContactId',
    'lastModifyById',
    'lastModifyName',
    'lastModifyPhone',
    'lastModifyEmail',
    'siteAddress',
    'billingAddress',
    'submitType',
    'estimateId',
    'referenceNumber',
    'documentStatus',
    'montantPaiement',
    'paiement',
    'zohoApiClientId',
    'zohoApiConnection',
    'booksCurrentSubStatusId',
    'booksCurrentSubStatus',
    'contact',
    'contactPerson',
    'vendeurCrm',
  ],
  DOCUMENT_PRODUCT_LINE_EXTRA_CREATE_DATA: [
    'prestationDateLine',
    'productLinePrices',
    'productLineSubOptions',
    'priceFamilyValue',
    'priceFamilyBuyingPrice',
    'priceFamilyPriceMargin',
    'priceFamilyValidSP',
    'buyingPrice',
    'priceMargin',
    'mainProductId',
    'parentKey',
    'lineOrderNumber',
  ],
  DOCUMENT_PRODUCT_LINE_EXTRA_UPDATE_DATA: [
    'prestationDateLine',
    'productLinePrices',
    'productLineSubOptions',
    'priceFamilyValue',
    'priceFamilyBuyingPrice',
    'priceFamilyPriceMargin',
    'priceFamilyValidSP',
    'linePriceFamilyId',
    'buyingPrice',
    'priceMargin',
  ],
  DOCUMENT_PRODUCT_LINES: [
    'headerName',
    'booksProductLineHeaderId',
    'buyingPrice',
    'priceMargin',
    'productId',
    'productNameForClient',
    'description',
    'descriptionWithParameter',
    'logComment',
    'prestationVueClient',
    'priceFamilyId',
    'quantity',
    'totalBeforeDiscount',
    'discount',
    'discountUnit',
    'productTypeUnitId',
    'productTypeUnitLabel',
    'unitPrice',
    'total',
    'isSetTotalZero',
    'lineOrderNumber',
    'updatedAt',
  ] as BulkCreateOptions<InferAttributes<DocumentProductLine, { omit: never }>>['updateOnDuplicate'],
  DOCUMENT_PRODUCT_LINE_PRESTATION_SUB_OPTION: [
    'id',
    'documentProductLinePrestationId',
    'subOptionId',
    'optionLabel',
    'subOptionLabel',
    'updatedAt',
  ] as BulkCreateOptions<InferAttributes<DocumentProductLinePrestationSubOption, { omit: never }>>['updateOnDuplicate'],
  DOCUMENT_PRODUCT_LINE_SUB_OPTION: [
    'id',
    'documentProductLineId',
    'subOptionId',
    'optionLabel',
    'subOptionLabel',
    'updatedAt',
  ] as BulkCreateOptions<InferAttributes<DocumentProductLineSubOption, { omit: never }>>['updateOnDuplicate'],
  DOCUMENT_PRODUCT_LINE_PRICE: [
    'id',
    'documentProductLineId',
    'priceId',
    'priceOptionId',
    'priceSubOptionId',
    'value',
    'buyingPrice',
    'priceMargin',
    'validSP',
    'priceLabel',
    'priceOptionLabel',
    'priceSubOptionLabel',
    'updatedAt',
  ] as BulkCreateOptions<InferAttributes<DocumentProductLinePrice, { omit: never }>>['updateOnDuplicate'],
  DOCUMENT_PRODUCT_LINE_PRESTATION: [
    'id',
    'documentProductLineId',
    'prestationDate',
    'booksPackageId',
    'booksShipmentId',
    'boOrderId',
    'isActive',
    'documentProductLinePrestationStatusId',
    'optionId',
    'optionLabel',
    'updatedAt',
  ] as BulkCreateOptions<InferAttributes<DocumentProductLinePrestation, { omit: never }>>['updateOnDuplicate'],
};

export const GOOGLE_MAPS_API_KEY = process.env.GOOGLE_MAPS_API_KEY;
export const COUNTRY_REGIONS = [
  {
    id: 1,
    name: 'Auvergne-Rhône-Alpes',
  },
  {
    id: 2,
    name: 'Bourgogne-Franche-Comté',
  },
  {
    id: 3,
    name: 'Bretagne',
  },
  {
    id: 4,
    name: 'Centre-Val de Loire',
  },
  { id: 5, name: 'Corse' },
  { id: 6, name: 'Grand Est' },
  {
    id: 7,
    name: 'Hauts-de-France',
  },
  {
    id: 8,
    name: 'Île-de-France',
  },
  { id: 9, name: 'Normandie' },
  {
    id: 10,
    name: 'Nouvelle-Aquitaine',
  },
  { id: 11, name: 'Occitanie' },
  {
    id: 12,
    name: 'Pays de la Loire',
  },
  {
    id: 13,
    name: "Provence-Alpes-Côte d'Azur",
  },
  { id: 14, name: 'Guadeloupe' },
  { id: 15, name: 'Guyane' },
  { id: 16, name: 'La Réunion' },
  { id: 17, name: 'Martinique' },
  { id: 18, name: 'Mayotte' },
];
