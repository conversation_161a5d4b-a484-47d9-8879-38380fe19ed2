import { APIGatewayProxyEvent } from 'aws-lambda';
import { middy, httpRouterHand<PERSON>, Route } from 'middy-layer';
import documentCCContactService from '../services/document-cc-contact.service';

const routes: Route<APIGatewayProxyEvent>[] = [
  {
    method: 'GET',
    path: '/document-cc-contacts',
    handler: documentCCContactService.paginate,
  },
  {
    method: 'POST',
    path: '/document-cc-contacts',
    handler: documentCCContactService.create,
  },
  {
    method: 'GET',
    path: '/document-cc-contacts/{id}',
    handler: documentCCContactService.getById,
  },
  {
    method: 'PUT',
    path: '/document-cc-contacts/{id}',
    handler: documentCCContactService.update,
  },
  {
    method: 'DELETE',
    path: '/document-cc-contacts/{id}',
    handler: documentCCContactService.delete,
  },
];

const handler = middy().handler(httpRouterHandler(routes));

export { handler };
