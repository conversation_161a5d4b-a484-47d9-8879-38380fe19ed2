import { APIGatewayProxyEvent } from 'aws-lambda';
import { middy, httpRouter<PERSON><PERSON><PERSON>, Route } from 'middy-layer';
import documentCCLibresContactService from '../services/document-cc-libres-contact.service';

const routes: Route<APIGatewayProxyEvent>[] = [
  {
    method: 'GET',
    path: '/document-cc-libres-contacts',
    handler: documentCCLibresContactService.paginate,
  },
  {
    method: 'POST',
    path: '/document-cc-libres-contacts',
    handler: documentCCLibresContactService.create,
  },
  {
    method: 'GET',
    path: '/document-cc-libres-contacts/{id}',
    handler: documentCCLibresContactService.getById,
  },
  {
    method: 'PUT',
    path: '/document-cc-libres-contacts/{id}',
    handler: documentCCLibresContactService.update,
  },
  {
    method: 'DELETE',
    path: '/document-cc-libres-contacts/{id}',
    handler: documentCCLibresContactService.delete,
  },
];

const handler = middy().handler(httpRouterHandler(routes));

export { handler };
