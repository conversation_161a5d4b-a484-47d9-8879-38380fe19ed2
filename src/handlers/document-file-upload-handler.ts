import { APIGatewayProxyEvent } from 'aws-lambda';
import { middy, httpRouterHand<PERSON>, Route } from 'middy-layer';
import documentFileUpLoadsService from '../services/document_file_uploads.service';

const routes: Route<APIGatewayProxyEvent>[] = [
  {
    method: 'GET',
    path: '/document-file-uploads',
    handler: documentFileUpLoadsService.paginate,
  },
  {
    method: 'GET',
    path: '/document-file-uploads/{id}',
    handler: documentFileUpLoadsService.getById,
  },
  {
    method: 'POST',
    path: '/document-file-uploads',
    handler: documentFileUpLoadsService.createUploadFile,
  },
  {
    method: 'PUT',
    path: '/document-file-uploads/{id}',
    handler: documentFileUpLoadsService.update,
  },
  {
    method: 'DELETE',
    path: '/document-file-uploads/{id}',
    handler: documentFileUpLoadsService.deleteUploadFile,
  },
];

export const handler = middy().handler(httpRouterHandler(routes));
