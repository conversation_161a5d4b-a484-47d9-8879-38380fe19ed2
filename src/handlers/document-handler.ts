import { APIGatewayProxyEvent } from 'aws-lambda';
import { middy, httpRouterHandler, Route } from 'middy-layer';
import DocumentService from '../services/document.service';

const service = new DocumentService();

const routes: Route<APIGatewayProxyEvent>[] = [
  {
    method: 'GET',
    path: '/documents',
    handler: service.paginate,
  },
  {
    method: 'GET',
    path: '/document/{id}',
    handler: service.getDocumentDetail,
  },
  {
    method: 'DELETE',
    path: '/document/{id}',
    handler: service.delete,
  },
  {
    method: 'POST',
    path: '/update-contact-document',
    handler: service.updateContactDocument,
  },
  {
    method: 'PUT',
    path: '/document-update-comment/{id}',
    handler: service.updateDocumentComment,
  },
  {
    method: 'PUT',
    path: '/zoho/quotation-update-log',
    handler: service.zohoUpdateDocument,
  },
  {
    method: 'PUT',
    path: '/document/{id}/status',
    handler: service.updateStatusDocument,
  },
  {
    method: 'PUT',
    path: '/document/update-country-region',
    handler: service.updateCountryRegionDocument,
  },
];

export const handler = middy().handler(httpRouterHandler(routes));
export const createOrUpdateDocument = service.createOrUpdateDocument;
