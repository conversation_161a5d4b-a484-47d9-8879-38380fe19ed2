import { APIGatewayProxyEvent } from 'aws-lambda';
import { middy, httpRouterH<PERSON><PERSON>, Route } from 'middy-layer';
import { DocumentProductLinePrestationService } from '../services/document_product_line_prestation.service';

const service = new DocumentProductLinePrestationService();

const routes: Route<APIGatewayProxyEvent>[] = [
  {
    method: 'GET',
    path: '/document-product-line-prestations',
    handler: service.paginate,
  },
  {
    method: 'GET',
    path: '/document-product-line-prestations/{id}',
    handler: service.getById,
  },
  {
    method: 'POST',
    path: '/document-product-line-prestations',
    handler: service.create,
  },
  {
    method: 'PUT',
    path: '/document-product-line-prestations/{id}',
    handler: service.update,
  },
  {
    method: 'DELETE',
    path: '/document-product-line-prestations/{id}',
    handler: service.delete,
  },
];

export const handler = middy().handler(httpRouterHandler(routes));
