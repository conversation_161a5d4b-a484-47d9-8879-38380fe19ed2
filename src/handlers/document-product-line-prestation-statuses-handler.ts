import { APIGatewayProxyEvent } from 'aws-lambda';
import { middy, httpRouterH<PERSON><PERSON>, Route } from 'middy-layer';
import { DocumentProductLinePrestationStatusService } from '../services/document_product_line_prestation_status.service';

const service = new DocumentProductLinePrestationStatusService();

const routes: Route<APIGatewayProxyEvent>[] = [
  {
    method: 'GET',
    path: '/document-product-line-prestation-statuses',
    handler: service.paginate,
  },
  {
    method: 'GET',
    path: '/document-product-line-prestation-statuses/{id}',
    handler: service.getById,
  },
  {
    method: 'POST',
    path: '/document-product-line-prestation-statuses',
    handler: service.create,
  },
  {
    method: 'PUT',
    path: '/document-product-line-prestation-statuses/{id}',
    handler: service.update,
  },
  {
    method: 'DELETE',
    path: '/document-product-line-prestation-statuses/{id}',
    handler: service.delete,
  },
];

export const handler = middy().handler(httpRouterHandler(routes));
