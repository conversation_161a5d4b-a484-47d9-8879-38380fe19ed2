import { APIGatewayProxyEvent } from 'aws-lambda';
import { middy, httpRouterHand<PERSON>, Route } from 'middy-layer';
import { DocumentProductLinePrestationSubOptionService } from '../services/document-product-line-prestation-sub-option.service';

const service = new DocumentProductLinePrestationSubOptionService();

const routes: Route<APIGatewayProxyEvent>[] = [
  {
    method: 'GET',
    path: '/document-product-line-prestation-sub-options',
    handler: service.paginate,
  },
  {
    method: 'GET',
    path: '/document-product-line-prestation-sub-options/{id}',
    handler: service.getById,
  },
  {
    method: 'POST',
    path: '/document-product-line-prestation-sub-options',
    handler: service.create,
  },
  {
    method: 'PUT',
    path: '/document-product-line-prestation-sub-options/{id}',
    handler: service.update,
  },
  {
    method: 'DELETE',
    path: '/document-product-line-prestation-sub-options/{id}',
    handler: service.delete,
  },
];

export const handler = middy().handler(httpRouterHandler(routes));
