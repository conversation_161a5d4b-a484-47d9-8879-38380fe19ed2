import { APIGatewayProxyEvent } from 'aws-lambda';
import { middy, httpRouterH<PERSON>ler, Route } from 'middy-layer';
import { DocumentProductLinePriceService } from '../services/document_product_line-prices.service';

const service = new DocumentProductLinePriceService();

const routes: Route<APIGatewayProxyEvent>[] = [
  {
    method: 'GET',
    path: '/document-product-line-price',
    handler: service.paginate,
  },
  {
    method: 'GET',
    path: '/document-product-line-price/{id}',
    handler: service.getById,
  },
  {
    method: 'POST',
    path: '/document-product-line-price',
    handler: service.create,
  },
  {
    method: 'PUT',
    path: '/document-product-line-price/{id}',
    handler: service.update,
  },
  {
    method: 'DELETE',
    path: '/document-product-line-price/{id}',
    handler: service.delete,
  },
];

export const handler = middy().handler(httpRouterHandler(routes));
