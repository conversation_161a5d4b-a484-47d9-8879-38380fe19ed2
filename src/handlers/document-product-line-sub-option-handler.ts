import { APIGatewayProxyEvent } from 'aws-lambda';
import { middy, httpRouterH<PERSON><PERSON>, Route } from 'middy-layer';
import { DocumentProductLineSubOptionService } from '../services/document-product-line-sub-option.service';

const service = new DocumentProductLineSubOptionService();

const routes: Route<APIGatewayProxyEvent>[] = [
  {
    method: 'GET',
    path: '/document-product-line-sub-options',
    handler: service.paginate,
  },
  {
    method: 'GET',
    path: '/document-product-line-sub-options/{id}',
    handler: service.getById,
  },
  {
    method: 'POST',
    path: '/document-product-line-sub-options',
    handler: service.create,
  },
  {
    method: 'PUT',
    path: '/document-product-line-sub-options/{id}',
    handler: service.update,
  },
  {
    method: 'DELETE',
    path: '/document-product-line-sub-options/{id}',
    handler: service.delete,
  },
];

export const handler = middy().handler(httpRouterHandler(routes));
