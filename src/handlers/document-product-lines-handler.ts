import { APIGatewayProxyEvent } from 'aws-lambda';
import { middy, httpRouterH<PERSON><PERSON>, Route } from 'middy-layer';
import { DocumentProductLinesService } from '../services/document_product_lines.service';

const service = new DocumentProductLinesService();

const routes: Route<APIGatewayProxyEvent>[] = [
  {
    method: 'GET',
    path: '/document-product-line',
    handler: service.paginate,
  },
  {
    method: 'GET',
    path: '/document-product-line/{id}',
    handler: service.getById,
  },
  {
    method: 'POST',
    path: '/document-product-line',
    handler: service.create,
  },
  {
    method: 'PUT',
    path: '/document-product-line/{id}',
    handler: service.update,
  },
  {
    method: 'DELETE',
    path: '/document-product-line/{id}',
    handler: service.delete,
  },
];

export const handler = middy().handler(httpRouterHandler(routes));
