import { APIGatewayProxyEvent } from 'aws-lambda';
import { middy, httpRouterHandler, Route } from 'middy-layer';
import { getDocumentStatusById, getDocumentStatuses } from '../services/document-status.service';

const routes: Route<APIGatewayProxyEvent>[] = [
  {
    method: 'GET',
    path: '/document-statuses',
    handler: getDocumentStatuses,
  },
  {
    method: 'GET',
    path: '/document-statuses/{id}',
    handler: getDocumentStatusById,
  },
];

const handler = middy().handler(httpRouterHandler(routes));

export { handler };
