import { APIGatewayProxyEvent } from 'aws-lambda';
import { middy, httpRouterHandler, Route } from 'middy-layer';
import { getDocumentTypes } from '../services/document-type.service';

const routes: Route<APIGatewayProxyEvent>[] = [
  {
    method: 'GET',
    path: '/document-types',
    handler: getDocumentTypes,
  },
];

const handler = middy().handler(httpRouterHandler(routes));

export { handler };
