import { APIGatewayProxyEvent } from 'aws-lambda';
import { middy, httpRouterHandler, Route } from 'middy-layer';
import {
  generateSignedUploadUrl,
  updateFileIntoS3,
  startMultipartUpload,
  completeMultipartUpload,
  generateSignedMultipartUpload,
  generateSignedGet,
} from '../services/file.service';

const routes: Route<APIGatewayProxyEvent>[] = [
  {
    method: 'POST',
    path: '/upload-file',
    handler: updateFileIntoS3,
  },
  {
    method: 'GET',
    path: '/pre-signed-url',
    handler: generateSignedUploadUrl,
  },
  {
    method: 'GET',
    path: '/start-multipart-upload',
    handler: startMultipartUpload,
  },
  {
    method: 'GET',
    path: '/pre-signed-url-multipart-upload',
    handler: generateSignedMultipartUpload,
  },
  {
    method: 'POST',
    path: '/complete-multipart-upload',
    handler: completeMultipartUpload,
  },
  {
    method: 'GET',
    path: '/pre-signed-url-get',
    handler: generateSignedGet,
  },
];

export const handler = middy().handler(httpRouterHandler(routes));
