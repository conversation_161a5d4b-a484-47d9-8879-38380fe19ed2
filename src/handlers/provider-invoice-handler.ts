import { APIGatewayProxyEvent } from 'aws-lambda';
import { middy, httpRouterHandler, Route } from 'middy-layer';
import { ProviderInvoiceService } from '../services/provider-invoice.service';

const service = new ProviderInvoiceService();

const routes: Route<APIGatewayProxyEvent>[] = [
  {
    method: 'GET',
    path: '/provider-invoices',
    handler: service.getInvoicesList,
  },
  {
    method: 'GET',
    path: '/provider-invoices/by-provider/{providerId}',
    handler: service.getInvoicesByProviderId,
  },
  {
    method: 'GET',
    path: '/provider-invoices/creation-data',
    handler: service.getInvoiceCreationData,
  },
  {
    method: 'GET',
    path: '/provider-invoices/{id}/edit',
    handler: service.getInvoiceDetailsEdit,
  },
  {
    method: 'POST',
    path: '/provider-invoices',
    handler: service.createInvoice,
  },
  {
    method: 'PUT',
    path: '/provider-invoices/{id}',
    handler: service.updateInvoice,
  },
];

export const handler = middy().handler(httpRouterHandler(routes));
