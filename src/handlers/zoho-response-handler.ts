import { SQSE<PERSON>, <PERSON>Q<PERSON>Hand<PERSON> } from 'aws-lambda';
import { ZohoBodyResponseEvent } from '../types';
import { DocumentFileUploadService } from '../services/document_file_uploads.service';

const documentFileUploadService = new DocumentFileUploadService();
export const handler: SQSHandler = async (event: SQSEvent) => {
  console.log(event);
  try {
    for (const record of event.Records) {
      console.log(record);
      const body: ZohoBodyResponseEvent = JSON.parse(record.body);
      switch (body?.requestData?.model) {
        case 'DocumentFileUpload':
          await documentFileUploadService.updateBooksFileId(body);
          break;
        default:
          break;
      }
    }
  } catch (error) {
    console.log(error);
  }
};
