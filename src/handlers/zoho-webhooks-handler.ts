import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'aws-lambda';
import { ZohoWebhookDataEvent, ZohoWebhookEvent } from '../types';
import { updateDocument, webhookDocument } from '../services/zoho-webhooks.service';
import { ErrorResponse, SuccessResponse } from 'database-layer';

export const handler: SQSHandler = async (event: SQSEvent) => {
  console.log(event);
  try {
    for (const record of event.Records) {
      console.log(record);
      const body: ZohoWebhookDataEvent = JSON.parse(record.body);
      const model = record.messageAttributes.eventModel.stringValue;
      const action = record.messageAttributes.actionType.stringValue;
      console.log('body: ', body);
      console.log('model: ', model);
      switch (model) {
        case 'Document':
          switch (action) {
            case 'UPDATE-FROM-ZOHO':
              await updateDocument(body);
              break;
            case 'UPDATE':
              await webhookDocument(body);
              break;
            default:
              break;
          }
          break;
        default:
          break;
      }
    }
  } catch (error) {
    console.log(error);
  }
};

export const updateDocumentFromZohoService = async (event: ZohoWebhookEvent) => {
  console.log('Received ZohoWebhookEvent >>>', event);
  const { data, model, action }: ZohoWebhookEvent = event;
  console.log('action >>>>>', action);
  console.log('model >>>>>', model);
  console.log('data >>>>>', data);
  try {
    switch (model) {
      case 'Document':
        switch (action) {
          case 'UPDATE-FROM-ZOHO':
            await updateDocument(data as ZohoWebhookDataEvent);
            break;
          default:
            break;
        }
        break;
      default:
        break;
    }
    console.log(`Update result from ${model}`);
    return new SuccessResponse(
      {
        isSuccess: true,
        message: `${model} updated successfully!`,
        data: data,
      },
      200,
    );
  } catch (error) {
    console.error(`Error updating ${model} from ZohoService:`, error);
    return new ErrorResponse(error?.body?.message || error?.message || `${model} update failed!`, data, 500);
  }
};
