'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('documents', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      documentTypeId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'document_types',
          key: 'id',
        },
      },
      cdeZoho: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      booksDocumentId: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      vendeur: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      referent: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      contactId: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      contactPersonId: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      bdcClient: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      contactSurPlace: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      billingAddressId: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      billingAddressFull: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      siteAddressId: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      siteAddressFull: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      siteAddressIsGps: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      siteAddressLatitude: {
        type: Sequelize.DECIMAL(10, 8),
        allowNull: true,
      },
      siteAddressLongitude: {
        type: Sequelize.DECIMAL(11, 8),
        allowNull: true,
      },
      objectDuDocument: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      demandeCommerciale: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      responseLogistique: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      prestationVueClient: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      priceDescription: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      logComment: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      documentStatusId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'document_statuses',
          key: 'id',
        },
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      deletedAt: {
        allowNull: true,
        type: Sequelize.Sequelize.DATE,
      },
    });

    await queryInterface.addIndex('documents', ['booksDocumentId']);
    await queryInterface.addIndex('documents', ['contactId']);
    await queryInterface.addIndex('documents', ['contactPersonId']);
    await queryInterface.addIndex('documents', ['billingAddressId']);
    await queryInterface.addIndex('documents', ['siteAddressId']);
  },
  async down(queryInterface) {
    await queryInterface.removeIndex('documents', ['booksDocumentId']);
    await queryInterface.removeIndex('documents', ['contactId']);
    await queryInterface.removeIndex('documents', ['contactPersonId']);
    await queryInterface.removeIndex('documents', ['billingAddressId']);
    await queryInterface.removeIndex('documents', ['siteAddressId']);
    await queryInterface.dropTable('documents');
  },
};
