'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('document_cc_contacts', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      documentId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'documents',
          key: 'id',
        },
      },
      contactPersonId: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      deletedAt: {
        allowNull: true,
        type: Sequelize.Sequelize.DATE,
      },
    });
    await queryInterface.addIndex('document_cc_contacts', ['contactPersonId']);
  },
  async down(queryInterface) {
    await queryInterface.removeIndex('document_cc_contacts', ['contactPersonId']);
    await queryInterface.dropTable('document_cc_contacts');
  },
};
