'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('document_product_lines', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      documentId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'documents',
          key: 'id',
        },
      },
      productId: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      productNameForClient: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      description: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      priceFamilyId: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      quantity: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      totalBeforeDiscount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true,
      },
      discount: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      total: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true,
      },
      isSetTotalZero: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
      },
      booksProductLineId: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      deletedAt: {
        allowNull: true,
        type: Sequelize.DATE,
      },
    });

    await queryInterface.addIndex('document_product_lines', ['documentId']);
    await queryInterface.addIndex('document_product_lines', ['productId']);
    await queryInterface.addIndex('document_product_lines', ['priceFamilyId']);
  },
  async down(queryInterface) {
    await queryInterface.removeIndex('document_product_lines', ['documentId']);
    await queryInterface.removeIndex('document_product_lines', ['productId']);
    await queryInterface.removeIndex('document_product_lines', ['priceFamilyId']);
    await queryInterface.dropTable('document_product_lines');
  },
};
