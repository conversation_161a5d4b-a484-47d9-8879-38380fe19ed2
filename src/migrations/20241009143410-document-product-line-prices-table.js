'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('document_product_line_prices', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      documentProductLineId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'document_product_lines',
          key: 'id',
        },
      },
      priceFamilyId: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      priceId: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      priceOptionId: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      value: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true,
      },
      isOptionPriceValue: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
      },
      priceFamilyLabel: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      priceLabel: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      priceOptionLabel: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      deletedAt: {
        allowNull: true,
        type: Sequelize.DATE,
      },
    });

    await queryInterface.addIndex('document_product_line_prices', ['documentProductLineId']);
    await queryInterface.addIndex('document_product_line_prices', ['priceFamilyId']);
    await queryInterface.addIndex('document_product_line_prices', ['priceId']);
    await queryInterface.addIndex('document_product_line_prices', ['priceOptionId']);
  },
  async down(queryInterface) {
    await queryInterface.removeIndex('document_product_line_prices', ['documentProductLineId']);
    await queryInterface.removeIndex('document_product_line_prices', ['priceFamilyId']);
    await queryInterface.removeIndex('document_product_line_prices', ['priceId']);
    await queryInterface.removeIndex('document_product_line_prices', ['priceOptionId']);
    await queryInterface.dropTable('document_product_line_prices');
  },
};
