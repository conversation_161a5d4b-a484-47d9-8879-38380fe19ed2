'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('document_product_line_prestations', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      documentProductLineId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'document_product_lines',
          key: 'id',
        },
      },
      prestationDate: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      booksPackageId: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      booksShipmentId: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      boOrderId: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
      },
      documentProductLinePrestationStatusId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'document_product_line_prestation_statuses',
          key: 'id',
        },
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      deletedAt: {
        allowNull: true,
        type: Sequelize.DATE,
      },
    });

    await queryInterface.addIndex('document_product_line_prestations', ['documentProductLineId']);
    await queryInterface.addIndex('document_product_line_prestations', ['booksPackageId']);
    await queryInterface.addIndex('document_product_line_prestations', ['booksShipmentId']);
  },
  async down(queryInterface) {
    await queryInterface.removeIndex('document_product_line_prestations', ['documentProductLineId']);
    await queryInterface.removeIndex('document_product_line_prestations', ['booksPackageId']);
    await queryInterface.removeIndex('document_product_line_prestations', ['booksShipmentId']);
    await queryInterface.dropTable('document_product_line_prestations');
  },
};
