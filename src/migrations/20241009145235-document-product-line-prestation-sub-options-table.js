'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('document_product_line_prestation_sub_options', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      documentProductLinePrestationId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'document_product_line_prestations',
          key: 'id',
        },
      },
      subOptionId: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      optionLabel: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      subOptionLabel: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      deletedAt: {
        allowNull: true,
        type: Sequelize.DATE,
      },
    });

    await queryInterface.addIndex('document_product_line_prestation_sub_options', ['subOptionId']);
  },
  async down(queryInterface) {
    await queryInterface.removeIndex('document_product_line_prestation_sub_options', ['subOptionId']);
    await queryInterface.dropTable('document_product_line_prestation_sub_options');
  },
};
