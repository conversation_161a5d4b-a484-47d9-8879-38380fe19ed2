'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.addColumn('document_product_line_prestations','optionId',  {
        type: Sequelize.INTEGER,
        allowNull: true,
        after: 'documentProductLinePrestationStatusId'
      });
      await queryInterface.addColumn('document_product_line_prestations','optionLabel',  {
        type: Sequelize.STRING,
        allowNull: true,
        after: 'optionId'
      });
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn('document_product_line_prestations', 'optionId');
      await queryInterface.removeColumn('document_product_line_prestations', 'optionLabel');
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};
