'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.addColumn('document_product_lines','headerName',  {
        type: Sequelize.INTEGER,
        allowNull: true,
        after: 'lineOrderNumber'
      });
      await queryInterface.addColumn('document_product_lines','booksProductLineHeaderId',  {
        type: Sequelize.INTEGER,
        allowNull: true,
        after: 'headerName'
      });
      await queryInterface.addColumn('document_product_lines','unitPrice',  {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true,
        after: 'booksProductLineHeaderId'
      });
      await queryInterface.addColumn('document_product_lines','discountUnit',  {
        type: Sequelize.STRING,
        allowNull: true,
        after: 'unitPrice'
      });
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn('document_product_lines', 'booksProductLineHeaderId');
      await queryInterface.removeColumn('document_product_lines', 'unitPrice');
      await queryInterface.removeColumn('document_product_lines', 'discountUnit');
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};
