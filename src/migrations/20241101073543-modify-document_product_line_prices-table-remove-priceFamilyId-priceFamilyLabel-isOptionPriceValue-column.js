'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface) {
    await queryInterface.removeColumn('document_product_line_prices', 'priceFamilyId');
    await queryInterface.removeColumn('document_product_line_prices', 'priceFamilyLabel');
    await queryInterface.removeColumn('document_product_line_prices', 'isOptionPriceValue');
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.addColumn('document_product_line_prices', 'priceFamilyId', {
      type: Sequelize.INTEGER,
      allowNull: true,
    });
    await queryInterface.addColumn('document_product_line_prices', 'priceFamilyLabel', {
      type: Sequelize.STRING,
      allowNull: true,
    });
    await queryInterface.addColumn('document_product_line_prices', 'isOptionPriceValue', {
      type: Sequelize.BOOLEAN,
      allowNull: true,
    });
  }
};
