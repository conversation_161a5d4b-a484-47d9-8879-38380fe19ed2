'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.addColumn('documents', 'genererLienDePaiement', {
        type: Sequelize.STRING,
        allowNull: true,
        after: 'documentStatusId'
      });
      await queryInterface.addColumn('documents', 'rappelDevis', {
        type: Sequelize.STRING,
        allowNull: true,
        after: 'genererLienDePaiement'
      });
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn('documents', 'genererLienDePaiement');
      await queryInterface.removeColumn('documents', 'rappelDevis');
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};
