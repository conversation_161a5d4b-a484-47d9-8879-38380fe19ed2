'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      const documentsTable = await queryInterface.describeTable('documents');
      if (!documentsTable?.siteAddressPostalCode) {
        await queryInterface.addColumn('documents', 'siteAddressPostalCode', {
          type: Sequelize.TEXT,
          allowNull: true,
          after: 'siteAddressId'
        });
      }
      if (!documentsTable?.siteAddressCity) {
        await queryInterface.addColumn('documents', 'siteAddressCity', {
          type: Sequelize.TEXT,
          allowNull: true,
          after: 'siteAddressPostalCode'
        });
      }
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn('documents', 'siteAddressPostalCode');
      await queryInterface.removeColumn('documents', 'siteAddressCity');
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};
