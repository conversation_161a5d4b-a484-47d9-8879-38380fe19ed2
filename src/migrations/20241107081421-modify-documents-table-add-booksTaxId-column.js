'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      const documentsTable = await queryInterface.describeTable('documents');
      if (!documentsTable?.booksTaxId) {
        await queryInterface.addColumn('documents', 'booksTaxId', {
          type: Sequelize.STRING,
          allowNull: true,
          after: 'rappelDevis'
        });
      }
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn('documents', 'booksTaxId');
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};
