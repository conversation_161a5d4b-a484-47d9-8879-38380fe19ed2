'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      const documentProductLinesTable = await queryInterface.describeTable('document_product_lines');
      if (!documentProductLinesTable?.priceFamilyLabel) {
        await queryInterface.addColumn('document_product_lines', 'priceFamilyLabel', {
          type: Sequelize.STRING,
          allowNull: true,
          after: 'priceFamilyId'
        });
      }
      if (!documentProductLinesTable?.descriptionWithParameter) {
        await queryInterface.addColumn('document_product_lines', 'descriptionWithParameter', {
          type: Sequelize.TEXT,
          allowNull: true,
          after: 'description'
        });
      }
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn('document_product_lines', 'priceFamilyLabel');
      await queryInterface.removeColumn('document_product_lines', 'descriptionWithParameter');
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};
