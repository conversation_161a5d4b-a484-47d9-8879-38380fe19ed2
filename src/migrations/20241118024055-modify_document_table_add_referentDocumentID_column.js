'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      const documentsTable = await queryInterface.describeTable('documents');
      if (!documentsTable?.referentDocumentID) {
        await queryInterface.addColumn('documents', 'referentDocumentID', {
          type: Sequelize.INTEGER,
          allowNull: true,
          after: 'id'
        });
      }
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn('documents', 'referentDocumentID');
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};
