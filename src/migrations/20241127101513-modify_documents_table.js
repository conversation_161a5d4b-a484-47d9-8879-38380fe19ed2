'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.addColumn('documents', 'paymentStatus', {
        type: Sequelize.STRING,
        allowNull: true,
        after: 'isAddressChantierTemporary'
      });
      await queryInterface.addColumn('documents', 'paymentType', {
        type: Sequelize.STRING,
        allowNull: true,
        after: 'paymentStatus'
      });
      await queryInterface.addColumn('documents', 'paiement', {
        type: Sequelize.STRING,
        allowNull: true,
        after: 'paymentType'
      });
      await queryInterface.addColumn('documents', 'montantPaiement', {
        type: Sequelize.STRING,
        allowNull: true,
        after: 'paiement'
      });
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down (queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn('documents', 'paymentStatus');
      await queryInterface.removeColumn('documents', 'paymentType');
      await queryInterface.removeColumn('documents', 'paiement');
      await queryInterface.removeColumn('documents', 'montantPaiement');
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
};
