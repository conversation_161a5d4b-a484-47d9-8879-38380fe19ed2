'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      const documentFileUploadsTable = await queryInterface.describeTable('document_file_uploads');
      if (!documentFileUploadsTable?.booksFileId) {
        await queryInterface.addColumn('document_file_uploads', 'booksFileId', {
          type: Sequelize.STRING,
          allowNull: true,
          after: 'type'
        });
      }
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn('document_file_uploads', 'booksFileId');
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};
