'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      const documentProductLines = await queryInterface.describeTable('document_product_lines');
      if (!documentProductLines?.logComment) {
        await queryInterface.addColumn('document_product_lines', 'logComment', {
          type: Sequelize.STRING,
          allowNull: true,
          after: 'descriptionWithParameter'
        });
      }
      if (!documentProductLines?.prestationVueClient) {
        await queryInterface.addColumn('document_product_lines', 'prestationVueClient', {
          type: Sequelize.STRING,
          allowNull: true,
          after: 'logComment'
        });
      }
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down (queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn('document_product_lines', 'logComment');
      await queryInterface.removeColumn('document_product_lines', 'prestationVueClient');
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
};
