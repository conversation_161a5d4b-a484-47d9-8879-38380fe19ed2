'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.addColumn('document_product_lines', 'productTypeUnitId', {
        type: Sequelize.INTEGER,
        allowNull: true,
        after: 'discountUnit',
      });
      await queryInterface.addIndex('document_product_lines', ['productTypeUnitId']);
      await queryInterface.addColumn('document_product_lines', 'productTypeUnitLabel', {
        type: Sequelize.STRING,
        allowNull: true,
        after: 'productTypeUnitId',
      });
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down (queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn('document_product_lines', 'productTypeUnitId');
      await queryInterface.removeIndex('document_product_lines', ['productTypeUnitId']);
      await queryInterface.removeColumn('document_product_lines', 'productTypeUnitLabel');
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
};
