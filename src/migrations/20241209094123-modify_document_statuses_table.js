'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.addColumn('document_statuses', 'status', {
        type: Sequelize.STRING,
        allowNull: true,
        after: 'key',
      });
      await queryInterface.addColumn('document_statuses', 'zohoStatus', {
        type: Sequelize.STRING,
        allowNull: true,
        after: 'status',
      });
      await queryInterface.addColumn('document_statuses', 'zohoCurrentSubStatus', {
        type: Sequelize.STRING,
        allowNull: true,
        after: 'zohoStatus',
      });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn('document_statuses', 'status');
      await queryInterface.removeColumn('document_statuses', 'zohoStatus');
      await queryInterface.removeColumn('document_statuses', 'zohoCurrentSubStatus');
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};
