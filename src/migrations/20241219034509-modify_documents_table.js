'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn('documents', 'booksCurrentSubStatusId');
      await queryInterface.removeColumn('documents', 'booksCurrentSubStatus');
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      const documentsTable = await queryInterface.describeTable('documents');
      if (!documentsTable?.booksCurrentSubStatusId) {
        await queryInterface.addColumn('documents', 'booksCurrentSubStatusId', {
          type: Sequelize.STRING,
          allowNull: true,
          after: 'genererLienDePaiement',
        });
      }
      if (!documentsTable?.booksCurrentSubStatus) {
        await queryInterface.addColumn('documents', 'booksCurrentSubStatus', {
          type: Sequelize.STRING,
          allowNull: true,
          after: 'booksCurrentSubStatusId',
        });
      }
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};
