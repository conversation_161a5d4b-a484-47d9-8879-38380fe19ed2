'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.changeColumn('document_product_lines', 'logComment', {
      type: Sequelize.TEXT,
      allowNull: true,
    });
    await queryInterface.changeColumn('document_product_lines', 'prestationVueClient', {
      type: Sequelize.TEXT,
      allowNull: true,
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.changeColumn('document_product_lines', 'logComment', {
      type: Sequelize.STRING,
      allowNull: true,
    });
    await queryInterface.changeColumn('document_product_lines', 'prestationVueClient', {
      type: Sequelize.STRING,
      allowNull: true,
    });
  }
};
