'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.addColumn('document_product_lines', 'buyingPrice', {
        type: Sequelize.FLOAT,
        allowNull: true,
        after: 'booksProductLineHeaderId',
      });
      await queryInterface.addColumn('document_product_lines', 'priceMargin', {
        type: Sequelize.FLOAT,
        allowNull: true,
        after: 'booksProductLineHeaderId',
      });
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn('document_product_lines', 'priceMargin');
      await queryInterface.removeColumn('document_product_lines', 'buyingPrice');
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};
