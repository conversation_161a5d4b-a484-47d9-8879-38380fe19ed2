'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.addColumn('document_product_line_prices', 'buyingPrice', {
        type: Sequelize.FLOAT,
        allowNull: true,
        after: 'priceOptionId',
      });
      await queryInterface.addColumn('document_product_line_prices', 'priceMargin', {
        type: Sequelize.FLOAT,
        allowNull: true,
        after: 'priceOptionId',
      });
      await queryInterface.addColumn('document_product_line_prices', 'validSP', {
        type: Sequelize.INTEGER,
        allowNull: true,
        after: 'priceOptionId',
      });
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn('document_product_line_prices', 'priceMargin');
      await queryInterface.removeColumn('document_product_line_prices', 'validSP');
      await queryInterface.removeColumn('document_product_line_prices', 'buyingPrice');
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};
