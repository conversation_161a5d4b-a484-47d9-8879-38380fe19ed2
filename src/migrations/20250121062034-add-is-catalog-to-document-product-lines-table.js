'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('document_product_lines', 'isCatalog', {
      type: Sequelize.BOOLEAN,
      allowNull: true,
      after: 'booksProductLineHeaderId',
    });
  },

  async down(queryInterface) {
    await queryInterface.removeColumn('document_product_lines', 'isCatalog');
  },
};
