'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.addColumn(
        'document_product_lines',
        'mainProductId',
        {
          type: Sequelize.INTEGER,
          allowNull: true,
          after: 'productNameForClient',
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'document_product_lines',
        'parentProductLineId',
        {
          type: Sequelize.INTEGER,
          allowNull: true,
          after: 'mainProductId',
          references: {
            model: 'document_product_lines',
            key: 'id',
          },
        },
        { transaction },
      );
      await queryInterface.addIndex('document_product_lines', ['mainProductId'], { transaction });
      await queryInterface.addIndex('document_product_lines', ['parentProductLineId'], { transaction });
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeIndex('document_product_lines', ['mainProductId'], { transaction });
      await queryInterface.removeIndex('document_product_lines', ['parentProductLineId'], { transaction });
      await queryInterface.removeColumn('document_product_lines', 'mainProductId', { transaction });
      await queryInterface.removeColumn('document_product_lines', 'parentProductLineId', { transaction });
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};
