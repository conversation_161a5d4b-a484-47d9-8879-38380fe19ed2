'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.addColumn('documents', 'syncStatus', {
        type: Sequelize.STRING,
        after: 'booksTaxId'
      });
      await queryInterface.addColumn('documents', 'syncMessage', {
        type: Sequelize.STRING,
        after: 'syncStatus'
      });
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down (queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn('documents', 'syncStatus');
      await queryInterface.removeColumn('documents', 'syncMessage');
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
};
