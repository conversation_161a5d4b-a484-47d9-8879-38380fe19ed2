'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface) {
    // Update the booksProductLineId for the document product line with id 4072
    // This is a temporary migration to fix the booksProductLineId for the document product line with id 4072 in ticket QBO-1570
    // https://ecodrop.atlassian.net/browse/QBO-1570
    await queryInterface.sequelize.query(`
      UPDATE ${queryInterface.sequelize.options.database}.document_product_lines
      SET booksProductLineId = "337859000314800798"
      WHERE id = 4072
    `);
  },

  async down(queryInterface) {
    await queryInterface.sequelize.query(`
      UPDATE ${queryInterface.sequelize.options.database}.document_product_lines
      SET booksProductLineId = NULL
      WHERE id = 4072
    `);
  },
};
