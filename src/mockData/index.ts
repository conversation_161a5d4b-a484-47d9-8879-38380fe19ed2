

// list for the search page 
export const invoiceList = [
  {
    id: 1,
    createdAt: '2025-05-10T09:15:00Z',
    createdBy: '<PERSON>',
    prestataire: 'EcoTrans S.A.e',
    prestataireInvoiceNumber: 'FAC-2025-3012',
    status: 'validée',
    totalSelected: 1320.00,
    totalAvoirs: 200.00,
    totalAmount: 1520.00,
    invoicedAmount: 2320.00,
  },
  {
    id: 2,
    createdAt: '2025-06-01T11:30:00Z',
    createdBy: '<PERSON><PERSON>',
    prestataire: 'DéchetsPro',
    prestataireInvoiceNumber: 'FAC-2025-8790',
    status: 'a_revoir',
    totalSelected: 1650.00,
    totalAvoirs: 0.00,
    totalAmount: 1650.00,
    invoicedAmount: 1650.00,
  },
  {
    id: 3,
    createdAt: '2025-06-20T08:00:00Z',
    createdBy: '<PERSON><PERSON> <PERSON>',
    prestataire: 'UrbanWaste',
    prestataireInvoiceNumber: 'FAC-2025-9032',
    status: 'brouillon',
    totalSelected: 980.00,
    totalAvoirs: 0,
    totalAmount: 980.00,
    invoicedAmount: 0.00,
  }
];

// for creation
export const commandePrestaCreate = [
  {
    id: 1,
    createdAt: null,
    prestataire: 'EcoTrans S.A.',
    createdBy: null,
    prestataireInvoiceNumber: null,
    status: null,
    totalSelected: null,
    totalAvoirs: null,
    totalAmount: null,
    invoicedAmount: null,
    avoirDetails: [
    ],
    commentDetails: [
    ],
    invoiceDetails: [
      {
        productName: 'Collecte déchets verts',
        productId: 12,
        invoiceNumber: 'INV-2025-4510',
        commandNumber: 'CMD-884533',
        siteAddress: '12 avenue des Champs',
        sitePostalCode: '75008',
        siteCity: 'Paris',
        prestationDetails: [
          { date: '2025-05-05', time: '10h-12h', type: 'Pose' },
          { date: '2025-05-05', time: '10h-12h', type: 'Retrait' }
        ],
        invoicedPrice: 720.00,
        clientPrice: 850.00,
        totalMargin: 18,
        totalDiffPurchase: 130,
        isSelected: false,
        contact: '<EMAIL>',
        invoiceLines: [
          {
            key: '1',
            productName: 'Déchets verts - Collecte 5m3',
            productDetails: 'Collecte avec camion léger',
            productId: 12,
            quantity: 1,
            unity: 'Unité',
            orderId: 'CMD-884533',
            zohoId: 'Z-45001',
            commandId: 'CDE-2025-45896',
            orderLink: '/commandes/CDE-2025-45896',
            purchasePrice: 600.00,
            invoicedPrice: 720.00,
            margin: 20,
            diffPurchase: 0,
            status: 'Réalisée',
            isSelected: false,
            isInvoiced: false,
            invoiceNumber: null,
            clientPrice: 850.00,
            isWaitingForPrestReply: false,
            comment: []
          },
          {
            key: '2',
            productName: 'Déchets verts - Collecte 5m3',
            productDetails: 'Collecte avec camion léger',
            productId: 12,
            quantity: 1,
            unity: 'Tonne',
            orderId: 'CMD-884533',
            zohoId: 'Z-45001',
            commandId: 'CDE-2025-45896',
            orderLink: '/commandes/CDE-2025-45896',
            purchasePrice: 600.00,
            invoicedPrice: 720.00,
            margin: 20,
            diffPurchase: 0,
            status: 'Réalisée',
            isSelected: false,
            isInvoiced: false,
            invoiceNumber: null,
            clientPrice: 850.00,
            isWaitingForPrestReply: false,
            comment: []
          },
          {
            key: '3',
            productName: 'Déchets verts - Collecte 5m3',
            productDetails: 'Collecte avec camion léger',
            productId: 12,
            quantity: 1,
            unity: 'Unité',
            orderId: 'CMD-884533',
            zohoId: 'Z-45001',
            commandId: 'CDE-2025-45896',
            orderLink: '/commandes/CDE-2025-45896',
            purchasePrice: 600.00,
            invoicedPrice: 720.00,
            margin: 20,
            diffPurchase: 0,
            status: 'Réalisée',
            isSelected: true,
            isInvoiced: false,
            invoiceNumber: null,
            clientPrice: 850.00,
            isWaitingForPrestReply: false,
            comment: []
          }
        ]
      },
      {
        productName: 'Benne gravats 15m3',
        productId: 45,
        invoiceNumber: 'INV-2025-4578',
        commandNumber: 'CMD-778812',
        siteAddress: '5 rue de Lyon',
        sitePostalCode: '69003',
        siteCity: 'Lyon',
        prestationDetails: [
          { date: '2025-05-28', time: '14h-16h', type: 'Pose' },
          { date: '2025-05-30', time: '9h-11h', type: 'Retrait' }
        ],
        invoicedPrice: 1650.00,
        clientPrice: 1950.00,
        totalMargin: 15,
        totalDiffPurchase: 0,
        isSelected: false,
        contact: '<EMAIL>',
        invoiceLines: [
          {
            key: '1',
            productName: 'Location Benne 15m3 - 3 jours',
            productDetails: 'Gravats seulement',
            productId: 45,
            quantity: 1,
            unity: 'Unité',
            orderId: 'CMD-778812',
            zohoId: 'Z-45002',
            commandId: 'CDE-2025-78901',
            orderLink: '/commandes/CDE-2025-78901',
            purchasePrice: 1300.00,
            invoicedPrice: 1650.00,
            margin: 21,
            diffPurchase: 0,
            status: 'Réalisée',
            isSelected: false,
            isInvoiced: true,
            invoiceNumber: 'Fac-2025-4578',
            clientPrice: 1950.00,
            isWaitingForPrestReply: false,
            comment: [
              {
                id: 1,
                createdBy: 'Nadia Bel',
                createdAt: '2025-06-10T10:15:00Z',
                comment: 'Vérifier la durée de location : 3 jours ou 5 jours ?'
              }
            ]
          },
          {
            key: '2',
            productName: 'Location Benne 15m3 - 3 jours',
            productDetails: 'Gravats seulement',
            productId: 45,
            quantity: 1,
            unity: 'Unité',
            orderId: 'CMD-778812',
            zohoId: 'Z-45002',
            commandId: 'CDE-2025-78901',
            orderLink: '/commandes/CDE-2025-78901',
            purchasePrice: 1300.00,
            invoicedPrice: 1650.00,
            margin: 21,
            diffPurchase: 0,
            status: 'Réalisée',
            isSelected: false,
            isInvoiced: false,
            invoiceNumber: null,
            clientPrice: 1950.00,
            isWaitingForPrestReply: false,
            comment: []
          }
        ]
      },
      {
        productName: 'Benne DIB 7m3',
        productId: 33,
        invoiceNumber: 'INV-2025-9990',
        commandNumber: 'CMD-900112',
        siteAddress: '22 rue Jules Ferry',
        sitePostalCode: '59800',
        siteCity: 'Lille',
        prestationDetails: [
          { date: '2025-06-15', time: '7h-9h', type: 'Pose' }
        ],
        invoicedPrice: 980.00,
        clientPrice: 1100.00,
        totalMargin: 11,
        totalDiffPurchase: 0,
        isSelected: false,
        contact: '<EMAIL>',
        invoiceLines: [
          {
            key: '1',
            productName: 'Location Benne DIB 7m3 - 2 jours',
            productDetails: 'Collecte DIB',
            productId: 33,
            quantity: 1,
            unity: 'Unité',
            orderId: 'CMD-900112',
            zohoId: 'Z-45003',
            commandId: 'CDE-2025-99988',
            orderLink: '/commandes/CDE-2025-99988',
            purchasePrice: 800.00,
            invoicedPrice: 980.00,
            margin: 18,
            diffPurchase: 0,
            status: 'Annulée',
            isSelected: false,
            isInvoiced: false,
            invoiceNumber: null,
            clientPrice: 1100.00,
            isWaitingForPrestReply: false,
            comment: [
              {
                id: 1,
                createdBy: 'Clara Petit',
                createdAt: '2025-06-25T14:10:00Z',
                comment: 'Facture identique à celle du 15/06. À ne pas payer.'
              }
            ]
          }
        ]
      }
    ]
  }
];

// for edit
export const commandePrestaEdit = [
  {
    id: 1,
    createdAt: '2025-05-10T09:15:00Z',
    prestataire: 'EcoTrans S.A.ee',
    createdBy: 'Jean Dupont',
    prestataireInvoiceNumber: 'FAC-2025-3012',
    status: 'a_revoir',
    totalSelected: 720,
    totalAvoirs: 200.00,
    totalAmount: 920.00,
    invoicedAmount: 1570.00,
    avoirDetails: [
      {
        id: 1,
        avoirNumber: 'AV-2025-1001',
        createdAt: '2025-06-10T12:00:00Z',
        amount: 150.00,
        createdBy: 'Léa Durand'
      },
      {
        id: 2,
        avoirNumber: 'AV-2025-1002',
        createdAt: '2025-05-16T12:00:00Z',
        amount: 50.00,
        createdBy: 'Jean Dupont'
      }
    ],
    commentDetails: [
      {
        id: 1,
        createdBy: 'Léa Durand',
        createdAt: '2025-06-11T08:45:00Z',
        comment: 'Avoir émis suite à surcoût facturé à tort.'
      }
    ],
    invoiceDetails: [
      {
        productName: 'Collecte déchets verts',
        productId: 12,
        invoiceNumber: 'INV-2025-4510',
        commandNumber: 'CMD-884533',
        siteAddress: '12 avenue des Champs',
        sitePostalCode: '75008',
        siteCity: 'Paris',
        prestationDetails: [
          { date: '2025-05-05', time: '10h-12h', type: 'Collecte' }
        ],
        invoicedPrice: 720.00,
        clientPrice: 850.00,
        totalMargin: 18,
        totalDiffPurchase: 0,
        isSelected: true,
        contact: '<EMAIL>',
        invoiceLines: [
          {
            key: '1',
            productName: 'Déchets verts - Collecte 5m3',
            productDetails: 'Collecte avec camion léger',
            productId: 12,
            quantity: 1,
            unity: 'Unité',
            orderId: 'CMD-884533',
            zohoId: 'Z-45001',
            commandId: 'CDE-2025-45896',
            orderLink: '/commandes/CDE-2025-45896',
            purchasePrice: 600.00,
            invoicedPrice: 720.00,
            margin: 20,
            diffPurchase: 0,
            status: 'Réalisée',
            isSelected: true,
            isInvoiced: false,
            invoiceNumber: null,
            clientPrice: 720.00,
            isWaitingForPrestReply: false,
            comment: []
          },
          {
            key: '2',
            productName: 'Déchets verts - Collecte 5m3',
            productDetails: 'Collecte avec camion léger',
            productId: 12,
            quantity: 1,
            unity: 'Unité',
            orderId: 'CMD-884533',
            zohoId: 'Z-45001',
            commandId: 'CDE-2025-45896',
            orderLink: '/commandes/CDE-2025-45896',
            purchasePrice: 600.00,
            invoicedPrice: 720.00,
            margin: 20,
            diffPurchase: 0,
            status: 'Réalisée',
            isSelected: false,
            isInvoiced: false,
            invoiceNumber: null,
            clientPrice: 720.00,
            isWaitingForPrestReply: false,
            comment: []
          },
          {
            key: '3',
            productName: 'Déchets verts - Collecte 5m3',
            productDetails: 'Collecte avec camion léger',
            productId: 12,
            quantity: 1,
            unity: 'Unité',
            orderId: 'CMD-884533',
            zohoId: 'Z-45001',
            commandId: 'CDE-2025-45896',
            orderLink: '/commandes/CDE-2025-45896',
            purchasePrice: 600.00,
            invoicedPrice: 720.00,
            margin: 20,
            diffPurchase: 0,
            status: 'Réalisée',
            isSelected: false,  
            isInvoiced: false,
            invoiceNumber: null,
            clientPrice: 720.00,
            isWaitingForPrestReply: false,
            comment: []
          }
        ]
      },
      {
        productName: 'Benne gravats 15m3',
        productId: 45,
        invoiceNumber: 'INV-2025-4578',
        commandNumber: 'CMD-778812',
        siteAddress: '5 rue de Lyon',
        sitePostalCode: '69003',
        siteCity: 'Lyon',
        prestationDetails: [
          { date: '2025-05-28', time: '14h-16h', type: 'Pose' },
          { date: '2025-05-30', time: '9h-11h', type: 'Retrait' }
        ],
        invoicedPrice: 1650.00,
        clientPrice: 1950.00,
        totalMargin: 15,
        totalDiffPurchase: 0,
        isSelected: false,
        contact: '<EMAIL>',
        invoiceLines: [
          {
            key: '1',
            productName: 'Location Benne 15m3 - 3 jours',
            productDetails: 'Gravats seulement',
            productId: 45,
            quantity: 1,
            unity: 'Unité',
            orderId: 'CMD-778812',
            zohoId: 'Z-45002',
            commandId: 'CDE-2025-78901',
            orderLink: '/commandes/CDE-2025-78901',
            purchasePrice: 1300.00,
            invoicedPrice: 1650.00,
            margin: 21,
            diffPurchase: 0,
            status: 'Réalisée',
            isSelected: false,
            isInvoiced: true,
            invoiceNumber: 'Fac-2025-4578',
            clientPrice: 1650.00,
            isWaitingForPrestReply: false,
            comment: [
              {
                id: 1,
                createdBy: 'Nadia Bel',
                createdAt: '2025-06-10T10:15:00Z',
                comment: 'Vérifier la durée de location : 3 jours ou 5 jours ?'
              }
            ]
          }
        ]
      },
      {
        productName: 'Benne DIB 7m3',
        productId: 33,
        invoiceNumber: 'INV-2025-9990',
        commandNumber: 'CMD-900112',
        siteAddress: '22 rue Jules Ferry',
        sitePostalCode: '59800',
        siteCity: 'Lille',
        prestationDetails: [
          { date: '2025-06-15', time: '7h-9h', type: 'Pose' }
        ],
        invoicedPrice: 980.00,
        clientPrice: 1100.00,
        totalMargin: 11,
        totalDiffPurchase: 0,
        isSelected: false,
        contact: '<EMAIL>',
        invoiceLines: [
          {
            key: '1',
            productName: 'Location Benne DIB 7m3 - 2 jours',
            productDetails: 'Collecte DIB',
            productId: 33,
            quantity: 1,
            unity: 'Unité',
            orderId: 'CMD-900112',
            zohoId: 'Z-45003',
            commandId: 'CDE-2025-99988',
            orderLink: '/commandes/CDE-2025-99988',
            purchasePrice: 800.00,
            invoicedPrice: 980.00,
            margin: 18,
            diffPurchase: 0,
            status: 'Annulée',
            isSelected: false,
            isInvoiced: false,
            invoiceNumber: null,
            clientPrice: 1100.00,
            isWaitingForPrestReply: false,
            comment: [
              {
                id: 1,
                createdBy: 'Clara Petit',
                createdAt: '2025-06-25T14:10:00Z',
                comment: 'Facture identique à celle du 15/06. À ne pas payer.'
              }
            ]
          }
        ]
      }
    ]
  }
];
