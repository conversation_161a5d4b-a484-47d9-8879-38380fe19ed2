import {
  sequelize,
  DataTypes,
  Model,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
  CreationAttributes,
  ForeignKey,
} from 'database-layer';
import { DocumentType, DocumentStatus, Demander, DocumentProductLine } from '../models';

export class Document extends Model<InferAttributes<Document>, InferCreationAttributes<Document>> {
  declare id: CreationOptional<number>;
  declare referentDocumentID: CreationOptional<number>;
  declare documentTypeId: ForeignKey<DocumentType['id']>;
  declare cdeZoho: CreationOptional<string>;
  declare booksDocumentId: CreationOptional<string>;
  declare vendeur: CreationOptional<number>;
  declare referent: CreationOptional<number>;
  declare contactId: CreationOptional<number>;
  declare contactPersonId: CreationOptional<number>;
  declare bdcClient: CreationOptional<string>;
  declare contactSurPlace: CreationOptional<string>;
  declare billingAddressId: CreationOptional<number>;
  declare billingAddressFull: CreationOptional<string>;
  declare siteAddressId: CreationOptional<number>;
  declare siteAddressPostalCode: CreationOptional<string>;
  declare siteAddressCity: CreationOptional<string>;
  declare siteCountryRegionId: CreationOptional<number>;
  declare siteAddressFull: CreationOptional<string>;
  declare siteAddressIsGps: CreationOptional<boolean>;
  declare siteAddressLatitude: CreationOptional<string>;
  declare siteAddressLongitude: CreationOptional<string>;
  declare objectDuDocument: CreationOptional<string>;
  declare demandeCommerciale: CreationOptional<string>;
  declare responseLogistique: CreationOptional<string>;
  declare prestationVueClient: CreationOptional<string>;
  declare priceDescription: CreationOptional<string>;
  declare logComment: CreationOptional<string>;
  declare comment: CreationOptional<string>;
  declare isActive: CreationOptional<boolean>;
  declare documentStatusId: ForeignKey<DocumentStatus['id']>;
  declare isAddressChantierTemporary: CreationOptional<boolean>;
  declare paymentStatus: CreationOptional<string>;
  declare paymentType: CreationOptional<string>;
  declare paiement: CreationOptional<string>;
  declare montantPaiement: CreationOptional<string>;
  declare conditionsDePaiement: CreationOptional<string>;
  declare retainerInvoiceSubTotal: CreationOptional<string>;
  declare retainerinvoiceId: CreationOptional<string>;
  declare createdFrom: 'Zoho' | 'QBO';
  declare latestPrestationDate: CreationOptional<Date>;
  declare genererLienDePaiement: CreationOptional<string>;
  declare rappelDevis: CreationOptional<string>;
  declare booksTaxId: CreationOptional<string>;
  declare syncStatus: CreationOptional<string>;
  declare syncMessage: CreationOptional<string>;
  declare createdAt: CreationOptional<Date>;
  declare updatedAt: CreationOptional<Date>;
  declare deletedAt: CreationOptional<Date>;

  DocumentProductLines?: DocumentProductLine[];
  DocumentType?: DocumentType;
  DocumentStatus?: DocumentStatus;
  declare Demander?: Demander;
}

export type DocumentInput = CreationAttributes<Document>;

Document.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      allowNull: false,
    },
    documentTypeId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: DocumentType,
        key: 'id',
      },
    },
    referentDocumentID: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    cdeZoho: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    booksDocumentId: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    vendeur: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    referent: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    contactId: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    contactPersonId: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    bdcClient: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    contactSurPlace: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    billingAddressId: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    billingAddressFull: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    siteAddressId: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    siteAddressCity: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    siteCountryRegionId: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    siteAddressPostalCode: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    siteAddressFull: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    siteAddressIsGps: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    siteAddressLatitude: {
      type: DataTypes.DECIMAL(10, 8),
      allowNull: true,
    },
    siteAddressLongitude: {
      type: DataTypes.DECIMAL(11, 8),
      allowNull: true,
    },
    objectDuDocument: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    demandeCommerciale: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    responseLogistique: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    prestationVueClient: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    priceDescription: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    logComment: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    comment: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    documentStatusId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: DocumentStatus,
        key: 'id',
      },
    },
    isAddressChantierTemporary: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    paymentStatus: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    paymentType: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    paiement: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    montantPaiement: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    conditionsDePaiement: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    retainerInvoiceSubTotal: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    retainerinvoiceId: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    createdFrom: {
      type: DataTypes.ENUM('Zoho', 'QBO'),
      defaultValue: 'QBO',
    },
    latestPrestationDate: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    genererLienDePaiement: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    rappelDevis: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    booksTaxId: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    syncStatus: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    syncMessage: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    tableName: 'documents',
    paranoid: true,
    modelName: 'Document',
    sequelize,
  },
);

export const readonlyAttributes = ['createdAt', 'updatedAt', 'deletedAt'];

export const DocumentAttributes = Object.keys(Document.getAttributes()).filter(
  (attr) => !readonlyAttributes.includes(attr),
);
