import {
  sequelize,
  DataTypes,
  Model,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
  CreationAttributes,
  ForeignKey,
} from 'database-layer';
import { Document } from './Document';
export class DocumentCCContact extends Model<
  InferAttributes<DocumentCCContact>,
  InferCreationAttributes<DocumentCCContact>
> {
  declare id: CreationOptional<number>;
  declare documentId: ForeignKey<Document['id']>;
  declare contactPersonId: number;
  declare createdAt: CreationOptional<Date>;
  declare updatedAt: CreationOptional<Date>;
  declare deletedAt: CreationOptional<Date>;
}

export type DocumentCCContactInput = CreationAttributes<DocumentCCContact>;

DocumentCCContact.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      allowNull: false,
    },
    documentId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: Document,
        key: 'id',
      },
    },
    contactPersonId: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    tableName: 'document_cc_contacts',
    paranoid: true,
    modelName: 'DocumentCCContact',
    sequelize,
  },
);

export const readonlyAttributes = ['createdAt', 'updatedAt', 'deletedAt'];

export const DocumentCCContactAttributes = Object.keys(DocumentCCContact.getAttributes()).filter(
  (attr) => !readonlyAttributes.includes(attr),
);
