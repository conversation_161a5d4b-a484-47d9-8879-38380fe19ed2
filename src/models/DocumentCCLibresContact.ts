import {
  sequelize,
  DataTypes,
  Model,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
  CreationAttributes,
  ForeignKey,
} from 'database-layer';
import { Document } from './Document';
export class DocumentCCLibresContact extends Model<
  InferAttributes<DocumentCCLibresContact>,
  InferCreationAttributes<DocumentCCLibresContact>
> {
  declare id: CreationOptional<number>;
  declare documentId: ForeignKey<Document['id']>;
  declare email: string;
  declare createdAt: CreationOptional<Date>;
  declare updatedAt: CreationOptional<Date>;
  declare deletedAt: CreationOptional<Date>;
}

export type DocumentCCLibresContactInput = CreationAttributes<DocumentCCLibresContact>;

DocumentCCLibresContact.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      allowNull: false,
    },
    documentId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: Document,
        key: 'id',
      },
    },
    email: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    tableName: 'document_cc_libres_contacts',
    paranoid: true,
    modelName: 'DocumentCCLibresContact',
    sequelize,
  },
);

export const readonlyAttributes = ['createdAt', 'updatedAt', 'deletedAt'];

export const DocumentCCLibresContactAttributes = Object.keys(DocumentCCLibresContact.getAttributes()).filter(
  (attr) => !readonlyAttributes.includes(attr),
);
