import {
  sequelize,
  DataTypes,
  Model,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
  CreationAttributes,
  ForeignKey,
} from 'database-layer';
import { Document } from './Document';
export class DocumentFileUpload extends Model<
  InferAttributes<DocumentFileUpload>,
  InferCreationAttributes<DocumentFileUpload>
> {
  declare id: CreationOptional<number>;
  declare documentId: ForeignKey<Document['id']>;
  declare name: string;
  declare url: string;
  declare type: string;
  declare booksFileId: CreationOptional<string>;
  declare createdAt: CreationOptional<Date>;
  declare updatedAt: CreationOptional<Date>;
  declare deletedAt: CreationOptional<Date>;
}

export type DocumentFileUploadInput = CreationAttributes<DocumentFileUpload>;

DocumentFileUpload.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      allowNull: false,
    },
    documentId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: Document,
        key: 'id',
      },
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    url: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    type: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    booksFileId: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    tableName: 'document_file_uploads',
    paranoid: true,
    modelName: 'DocumentFileUpload',
    sequelize,
  },
);

export const readonlyAttributes = ['createdAt', 'updatedAt', 'deletedAt'];

export const DocumentFileUploadAttributes = Object.keys(DocumentFileUpload.getAttributes()).filter(
  (attr) => !readonlyAttributes.includes(attr),
);
