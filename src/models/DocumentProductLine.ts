import {
  sequelize,
  DataTypes,
  Model,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
  CreationAttributes,
  ForeignKey,
} from 'database-layer';
import { Document } from '../models';

export class DocumentProductLine extends Model<
  InferAttributes<DocumentProductLine>,
  InferCreationAttributes<DocumentProductLine>
> {
  declare id: CreationOptional<number>;
  declare documentId: ForeignKey<Document['id']>;
  declare productId: CreationOptional<number>;
  declare productNameForClient: CreationOptional<string>;
  declare mainProductId: CreationOptional<number>;
  declare parentProductLineId: CreationOptional<number>;
  declare description: CreationOptional<string>;
  declare descriptionWithParameter: CreationOptional<string>;
  declare logComment: CreationOptional<string>;
  declare prestationVueClient: CreationOptional<string>;
  declare priceFamilyId: CreationOptional<number>;
  declare priceFamilyLabel: CreationOptional<string>;
  declare quantity: CreationOptional<number>;
  declare totalBeforeDiscount: CreationOptional<number>;
  declare discount: CreationOptional<number>;
  declare total: CreationOptional<number>;
  declare isSetTotalZero: CreationOptional<boolean>;
  declare booksProductLineId: CreationOptional<string>;
  declare lineOrderNumber: CreationOptional<number>;
  declare headerName: CreationOptional<string>;
  declare booksProductLineHeaderId: CreationOptional<string>;
  declare isCatalog: CreationOptional<boolean>;
  declare buyingPrice: CreationOptional<number>;
  declare priceMargin: CreationOptional<number>;
  declare unitPrice: CreationOptional<number>;
  declare discountUnit: CreationOptional<string>;
  declare productTypeUnitId: CreationOptional<number>;
  declare productTypeUnitLabel: CreationOptional<string>;
  declare interventionId: CreationOptional<number>;
  declare interventionKey: CreationOptional<string>;
  declare clientOrderProductStatus: CreationOptional<string>;
  declare serviceProviderOrderQuantity: CreationOptional<number>;
  declare createdAt: CreationOptional<Date>;
  declare updatedAt: CreationOptional<Date>;
  declare deletedAt: CreationOptional<Date>;
}

export type DocumentProductLineInput = CreationAttributes<DocumentProductLine>;

DocumentProductLine.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      allowNull: false,
    },
    documentId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'documents',
        key: 'id',
      },
    },
    productId: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    productNameForClient: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    mainProductId: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    parentProductLineId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'document_product_lines',
        key: 'id',
      },
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    descriptionWithParameter: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    logComment: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    prestationVueClient: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    priceFamilyId: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    priceFamilyLabel: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    quantity: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
    },
    totalBeforeDiscount: {
      type: DataTypes.DECIMAL(10, 8),
      allowNull: true,
    },
    discount: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    total: {
      type: DataTypes.DECIMAL(10, 8),
      allowNull: true,
    },
    isSetTotalZero: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
    },
    booksProductLineId: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    lineOrderNumber: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    headerName: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    booksProductLineHeaderId: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    isCatalog: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
    },
    buyingPrice: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    priceMargin: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    unitPrice: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    discountUnit: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    productTypeUnitId: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    productTypeUnitLabel: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    interventionId: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    interventionKey: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    clientOrderProductStatus: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    serviceProviderOrderQuantity: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    tableName: 'document_product_lines',
    paranoid: true,
    modelName: 'DocumentProductLine',
    sequelize,
  },
);

export const readonlyAttributes = ['createdAt', 'updatedAt', 'deletedAt'];

export const DocumentProductLineAttributes = Object.keys(DocumentProductLine.getAttributes()).filter(
  (attr) => !readonlyAttributes.includes(attr),
);
