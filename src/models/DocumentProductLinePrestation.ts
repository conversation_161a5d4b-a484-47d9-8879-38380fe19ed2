import {
  sequelize,
  DataTypes,
  Model,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
  CreationAttributes,
  ForeignKey,
} from 'database-layer';
import { DocumentProductLine, DocumentProductLinePrestationStatus } from '../models';

export class DocumentProductLinePrestation extends Model<
  InferAttributes<DocumentProductLinePrestation>,
  InferCreationAttributes<DocumentProductLinePrestation>
> {
  declare id: CreationOptional<number>;
  declare documentProductLineId: ForeignKey<DocumentProductLine['id']>;
  declare prestationDate: CreationOptional<Date>;
  declare booksPackageId: CreationOptional<string>;
  declare booksShipmentId: CreationOptional<string>;
  declare boOrderId: CreationOptional<string>;
  declare isActive: CreationOptional<boolean>;
  declare documentProductLinePrestationStatusId: ForeignKey<DocumentProductLinePrestationStatus['id']>;
  declare optionId: CreationOptional<string>;
  declare optionLabel: CreationOptional<string>;
  declare createdAt: CreationOptional<Date>;
  declare updatedAt: CreationOptional<Date>;
  declare deletedAt: CreationOptional<Date>;
}

export type DocumentProductLinePrestationInput = CreationAttributes<DocumentProductLinePrestation>;

DocumentProductLinePrestation.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      allowNull: false,
    },
    documentProductLineId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'document_product_lines',
        key: 'id',
      },
    },
    prestationDate: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    booksPackageId: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    booksShipmentId: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    boOrderId: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
    },
    documentProductLinePrestationStatusId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'document_product_line_prestation_statuses',
        key: 'id',
      },
    },
    optionId: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    optionLabel: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    tableName: 'document_product_line_prestations',
    paranoid: true,
    modelName: 'DocumentProductLinePrestation',
    sequelize,
  },
);

export const readonlyAttributes = ['createdAt', 'updatedAt', 'deletedAt'];

export const DocumentProductLinePrestationAttributes = Object.keys(
  DocumentProductLinePrestation.getAttributes(),
).filter((attr) => !readonlyAttributes.includes(attr));
