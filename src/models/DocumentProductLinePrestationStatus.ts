import {
  sequelize,
  DataTypes,
  Model,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
  CreationAttributes,
} from 'database-layer';

export class DocumentProductLinePrestationStatus extends Model<
  InferAttributes<DocumentProductLinePrestationStatus>,
  InferCreationAttributes<DocumentProductLinePrestationStatus>
> {
  declare id: CreationOptional<number>;
  declare name: CreationOptional<string>;
  declare key: CreationOptional<string>;
  declare createdAt: CreationOptional<Date>;
  declare updatedAt: CreationOptional<Date>;
  declare deletedAt: CreationOptional<Date>;
}

export type DocumentProductLinePrestationStatusInput = CreationAttributes<DocumentProductLinePrestationStatus>;

DocumentProductLinePrestationStatus.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    key: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    tableName: 'document_product_line_prestation_statuses',
    paranoid: true,
    modelName: 'DocumentProductLinePrestationStatus',
    sequelize,
  },
);

export const readonlyAttributes = ['createdAt', 'updatedAt', 'deletedAt'];

export const DocumentProductLinePrestationStatusAttributes = Object.keys(
  DocumentProductLinePrestationStatus.getAttributes(),
).filter((attr) => !readonlyAttributes.includes(attr));
