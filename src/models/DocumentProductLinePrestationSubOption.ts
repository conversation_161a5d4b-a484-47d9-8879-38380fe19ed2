import {
  sequelize,
  DataTypes,
  Model,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
  CreationAttributes,
  ForeignKey,
} from 'database-layer';
import { DocumentProductLinePrestation } from '../models';

export class DocumentProductLinePrestationSubOption extends Model<
  InferAttributes<DocumentProductLinePrestationSubOption>,
  InferCreationAttributes<DocumentProductLinePrestationSubOption>
> {
  declare id: CreationOptional<number>;
  declare documentProductLinePrestationId: ForeignKey<DocumentProductLinePrestation['id']>;
  declare subOptionId: CreationOptional<number>;
  declare optionLabel: CreationOptional<string>;
  declare subOptionLabel: CreationOptional<string>;
  declare createdAt: CreationOptional<Date>;
  declare updatedAt: CreationOptional<Date>;
  declare deletedAt: CreationOptional<Date>;
}

export type DocumentProductLinePrestationSubOptionInput = CreationAttributes<DocumentProductLinePrestationSubOption>;

DocumentProductLinePrestationSubOption.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      allowNull: false,
    },
    documentProductLinePrestationId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'document_product_line_prestations',
        key: 'id',
      },
    },
    subOptionId: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    optionLabel: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    subOptionLabel: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    tableName: 'document_product_line_prestation_sub_options',
    paranoid: true,
    modelName: 'DocumentProductLinePrestationSubOption',
    sequelize,
  },
);

export const readonlyAttributes = ['createdAt', 'updatedAt', 'deletedAt'];

export const DocumentProductLinePrestationSubOptionAttributes = Object.keys(
  DocumentProductLinePrestationSubOption.getAttributes(),
).filter((attr) => !readonlyAttributes.includes(attr));
