import {
  sequelize,
  DataTypes,
  Model,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
  CreationAttributes,
  ForeignKey,
} from 'database-layer';
import { DocumentProductLine } from '../models';

export class DocumentProductLinePrice extends Model<
  InferAttributes<DocumentProductLinePrice>,
  InferCreationAttributes<DocumentProductLinePrice>
> {
  declare id: CreationOptional<number>;
  declare documentProductLineId: ForeignKey<DocumentProductLine['id']>;
  declare priceId: CreationOptional<number>;
  declare priceOptionId: CreationOptional<number>;
  declare buyingPrice: CreationOptional<number>;
  declare priceMargin: CreationOptional<number>;
  declare validSP: CreationOptional<number>;
  declare value: CreationOptional<number>;
  declare priceLabel: CreationOptional<string>;
  declare priceOptionLabel: CreationOptional<string>;
  declare priceSubOptionId: CreationOptional<string>;
  declare priceSubOptionLabel: CreationOptional<string>;
  declare createdAt: CreationOptional<Date>;
  declare updatedAt: CreationOptional<Date>;
  declare deletedAt: CreationOptional<Date>;
}

export type DocumentProductLinePriceInput = CreationAttributes<DocumentProductLinePrice>;

DocumentProductLinePrice.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      allowNull: false,
    },
    documentProductLineId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'document_product_lines',
        key: 'id',
      },
    },
    priceId: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    priceOptionId: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    buyingPrice: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    priceMargin: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    validSP: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    value: {
      type: DataTypes.DECIMAL(10, 8),
      allowNull: true,
    },
    priceLabel: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    priceOptionLabel: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    priceSubOptionId: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    priceSubOptionLabel: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    tableName: 'document_product_line_prices',
    paranoid: true,
    modelName: 'DocumentProductLinePrice',
    sequelize,
  },
);

export const readonlyAttributes = ['createdAt', 'updatedAt', 'deletedAt'];

export const DocumentProductLinePriceAttributes = Object.keys(DocumentProductLinePrice.getAttributes()).filter(
  (attr) => !readonlyAttributes.includes(attr),
);
