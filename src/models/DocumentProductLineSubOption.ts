import {
  sequelize,
  DataTypes,
  Model,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
  CreationAttributes,
  ForeignKey,
} from 'database-layer';
import { DocumentProductLine } from '../models';

export class DocumentProductLineSubOption extends Model<
  InferAttributes<DocumentProductLineSubOption>,
  InferCreationAttributes<DocumentProductLineSubOption>
> {
  declare id: CreationOptional<number>;
  declare documentProductLineId: ForeignKey<DocumentProductLine['id']>;
  declare subOptionId: CreationOptional<number>;
  declare optionLabel: CreationOptional<string>;
  declare subOptionLabel: CreationOptional<string>;
  declare createdAt: CreationOptional<Date>;
  declare updatedAt: CreationOptional<Date>;
  declare deletedAt: CreationOptional<Date>;
}

export type DocumentProductLineSubOptionInput = CreationAttributes<DocumentProductLineSubOption>;

DocumentProductLineSubOption.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      allowNull: false,
    },
    documentProductLineId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'document_product_line_s',
        key: 'id',
      },
    },
    subOptionId: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    optionLabel: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    subOptionLabel: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    tableName: 'document_product_line_sub_options',
    paranoid: true,
    modelName: 'DocumentProductLineSubOption',
    sequelize,
  },
);

export const readonlyAttributes = ['createdAt', 'updatedAt', 'deletedAt'];

export const DocumentProductLineSubOptionAttributes = Object.keys(DocumentProductLineSubOption.getAttributes()).filter(
  (attr) => !readonlyAttributes.includes(attr),
);
