import {
  sequelize,
  DataTypes,
  Model,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
  CreationAttributes,
} from 'database-layer';
export class DocumentStatus extends Model<InferAttributes<DocumentStatus>, InferCreationAttributes<DocumentStatus>> {
  declare id: CreationOptional<number>;
  declare name: string;
  declare key: string;
  declare status: string;
  declare zohoStatus: string;
  declare zohoCurrentSubStatus: string;
  declare createdAt: CreationOptional<Date>;
  declare updatedAt: CreationOptional<Date>;
  declare deletedAt: CreationOptional<Date>;
}

export type DocumentStatusInput = CreationAttributes<DocumentStatus>;

DocumentStatus.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    key: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    status: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    zohoStatus: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    zohoCurrentSubStatus: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    tableName: 'document_statuses',
    paranoid: true,
    modelName: 'DocumentStatus',
    sequelize,
  },
);

export const readonlyAttributes = ['createdAt', 'updatedAt', 'deletedAt'];

export const DocumentStatusAttributes = Object.keys(DocumentStatus.getAttributes()).filter(
  (attr) => !readonlyAttributes.includes(attr),
);
