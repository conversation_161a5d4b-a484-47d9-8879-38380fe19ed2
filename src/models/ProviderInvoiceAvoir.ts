import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from 'database-layer';

export interface ProviderInvoiceAvoirAttributes {
  id: number;
  documentId: number;
  avoirNumber: string;
  amount: number;
  createdBy?: string;
  createdAt: Date;
  updatedAt: Date;
}

export type ProviderInvoiceAvoirInput = Optional<ProviderInvoiceAvoirAttributes, 'id' | 'createdAt' | 'updatedAt'>;

export class ProviderInvoiceAvoir extends Model<ProviderInvoiceAvoirAttributes, ProviderInvoiceAvoirInput> implements ProviderInvoiceAvoirAttributes {
  public id!: number;
  public documentId!: number;
  public avoirNumber!: string;
  public amount!: number;
  public createdBy?: string;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

ProviderInvoiceAvoir.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    documentId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    avoirNumber: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    createdBy: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    sequelize,
    tableName: 'provider_invoice_avoirs',
    modelName: 'ProviderInvoiceAvoir',
    timestamps: true,
  }
);
