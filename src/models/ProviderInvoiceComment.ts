import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from 'database-layer';

export interface ProviderInvoiceCommentAttributes {
  id: number;
  documentId: number;
  comment: string;
  createdBy?: string;
  createdAt: Date;
  updatedAt: Date;
}

export type ProviderInvoiceCommentInput = Optional<ProviderInvoiceCommentAttributes, 'id' | 'createdAt' | 'updatedAt'>;

export class ProviderInvoiceComment extends Model<ProviderInvoiceCommentAttributes, ProviderInvoiceCommentInput> implements ProviderInvoiceCommentAttributes {
  public id!: number;
  public documentId!: number;
  public comment!: string;
  public createdBy?: string;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

ProviderInvoiceComment.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    documentId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    comment: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    createdBy: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    sequelize,
    tableName: 'provider_invoice_comments',
    modelName: 'ProviderInvoiceComment',
    timestamps: true,
  }
);
