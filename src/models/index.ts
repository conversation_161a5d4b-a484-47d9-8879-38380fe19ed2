import { DocumentType, DocumentTypeInput, DocumentTypeAttributes } from './DocumentType';
import { DocumentStatus, DocumentStatusInput, DocumentStatusAttributes } from './DocumentStatus';
import { Document, DocumentAttributes, DocumentInput } from './Document';
import { DocumentCCContact, DocumentCCContactAttributes, DocumentCCContactInput } from './DocumentCCContact';
import { DocumentFileUpload, DocumentFileUploadAttributes, DocumentFileUploadInput } from './DocumentFileUpload';
import { DocumentProductLine, DocumentProductLineAttributes, DocumentProductLineInput } from './DocumentProductLine';
import { Contact } from './readonly/Contact';
import { Product } from './readonly/Product';
import { Sale } from './readonly/Sale';
import {
  DocumentProductLinePrice,
  DocumentProductLinePriceAttributes,
  DocumentProductLinePriceInput,
} from './DocumentProductLinePrice';
import {
  DocumentProductLinePrestation,
  DocumentProductLinePrestationAttributes,
  DocumentProductLinePrestationInput,
} from './DocumentProductLinePrestation';
import {
  DocumentProductLinePrestationStatus,
  DocumentProductLinePrestationStatusAttributes,
  DocumentProductLinePrestationStatusInput,
} from './DocumentProductLinePrestationStatus';
import {
  DocumentProductLinePrestationSubOption,
  DocumentProductLinePrestationSubOptionAttributes,
  DocumentProductLinePrestationSubOptionInput,
} from './DocumentProductLinePrestationSubOption';
import {
  DocumentProductLineSubOption,
  DocumentProductLineSubOptionAttributes,
  DocumentProductLineSubOptionInput,
} from './DocumentProductLineSubOption';
import {
  DocumentCCLibresContact,
  DocumentCCLibresContactAttributes,
  DocumentCCLibresContactInput,
} from './DocumentCCLibresContact';
import { Demander } from './Demander';
import { ProviderInvoiceComment, ProviderInvoiceCommentAttributes, ProviderInvoiceCommentInput } from './ProviderInvoiceComment';
import { ProviderInvoiceAvoir, ProviderInvoiceAvoirAttributes, ProviderInvoiceAvoirInput } from './ProviderInvoiceAvoir';

export {
  DocumentType,
  DocumentTypeInput,
  DocumentTypeAttributes,
  DocumentStatus,
  DocumentStatusAttributes,
  DocumentStatusInput,
  Document,
  DocumentAttributes,
  DocumentInput,
  DocumentCCContact,
  DocumentCCContactAttributes,
  DocumentCCContactInput,
  DocumentFileUpload,
  DocumentFileUploadAttributes,
  DocumentFileUploadInput,
  DocumentCCLibresContact,
  DocumentCCLibresContactAttributes,
  DocumentCCLibresContactInput,
  DocumentProductLine,
  DocumentProductLineAttributes,
  DocumentProductLineInput,
  DocumentProductLinePrice,
  DocumentProductLinePriceAttributes,
  DocumentProductLinePriceInput,
  DocumentProductLinePrestation,
  DocumentProductLinePrestationAttributes,
  DocumentProductLinePrestationInput,
  DocumentProductLinePrestationStatus,
  DocumentProductLinePrestationStatusAttributes,
  DocumentProductLinePrestationStatusInput,
  DocumentProductLinePrestationSubOption,
  DocumentProductLinePrestationSubOptionAttributes,
  DocumentProductLinePrestationSubOptionInput,
  DocumentProductLineSubOption,
  DocumentProductLineSubOptionAttributes,
  DocumentProductLineSubOptionInput,
  Demander,
  Contact,
  Product,
  Sale,
  ProviderInvoiceComment,
  ProviderInvoiceCommentAttributes,
  ProviderInvoiceCommentInput,
  ProviderInvoiceAvoir,
  ProviderInvoiceAvoirAttributes,
  ProviderInvoiceAvoirInput,
};
//DocumentCCLibresContact
DocumentCCLibresContact.belongsTo(Document, { foreignKey: 'documentId' });
//DocumentProductLine
DocumentProductLine.hasMany(DocumentProductLinePrice, { foreignKey: 'documentProductLineId' });
DocumentProductLine.hasMany(DocumentProductLinePrestation, { foreignKey: 'documentProductLineId' });
DocumentProductLine.hasMany(DocumentProductLineSubOption, { foreignKey: 'documentProductLineId' });
DocumentProductLine.belongsTo(Document, { foreignKey: 'documentId' });
//DocumentProductLinePrice
DocumentProductLinePrice.belongsTo(DocumentProductLine, { foreignKey: 'documentProductLineId' });
//DocumentProductLinePrestation
DocumentProductLinePrestation.belongsTo(DocumentProductLine, { foreignKey: 'documentProductLineId' });
DocumentProductLinePrestation.belongsTo(DocumentProductLinePrestationStatus, {
  foreignKey: 'documentProductLinePrestationStatusId',
});
DocumentProductLinePrestation.hasMany(DocumentProductLinePrestationSubOption, {
  foreignKey: 'documentProductLinePrestationId',
});
//DocumentProductLinePrestationStatus
DocumentProductLinePrestationStatus.hasMany(DocumentProductLinePrestation, {
  foreignKey: 'documentProductLinePrestationStatusId',
});
//DocumentProductLinePrestationSubOption
DocumentProductLinePrestationSubOption.belongsTo(DocumentProductLinePrestation, {
  foreignKey: 'documentProductLinePrestationId',
});
//DocumentProductLineSubOption
DocumentProductLineSubOption.belongsTo(DocumentProductLine, { foreignKey: 'documentProductLineId' });
//Document
Document.hasMany(DocumentCCLibresContact, { foreignKey: 'documentId' });
Document.hasMany(DocumentCCContact, { foreignKey: 'documentId' });
Document.hasMany(DocumentProductLine, { foreignKey: 'documentId' });
Document.belongsTo(DocumentType, { foreignKey: 'documentTypeId' });
Document.belongsTo(DocumentStatus, { foreignKey: 'documentStatusId' });
Document.hasMany(DocumentFileUpload, { foreignKey: 'documentId' });
Document.hasOne(Document, { foreignKey: 'referentDocumentID', as: 'QuotationDocument' });
Document.belongsTo(Document, { foreignKey: 'referentDocumentID', as: 'EstimateDocument' });
// DocumentType
DocumentType.hasMany(Document, { foreignKey: 'documentTypeId' });
// DocumentStatus
DocumentStatus.hasMany(Document, { foreignKey: 'documentStatusId' });
Document.belongsTo(Contact, { foreignKey: 'contactId' });
Document.belongsTo(Sale, { foreignKey: 'vendeur', as: 'Vendeur' });
Document.belongsTo(Sale, { foreignKey: 'referent', as: 'Referent' });

// Provider Invoice relationships
Document.hasMany(ProviderInvoiceComment, { foreignKey: 'documentId' });
ProviderInvoiceComment.belongsTo(Document, { foreignKey: 'documentId' });

Document.hasMany(ProviderInvoiceAvoir, { foreignKey: 'documentId' });
ProviderInvoiceAvoir.belongsTo(Document, { foreignKey: 'documentId' });
