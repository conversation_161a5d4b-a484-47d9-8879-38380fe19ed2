import {
  sequelize,
  DataTypes,
  Model,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
  CreationAttributes,
} from 'database-layer';
export class Contact extends Model<InferAttributes<Contact>, InferCreationAttributes<Contact>> {
  declare id: CreationOptional<number>;
  declare clientType: 'business' | 'individual';
  declare name: string;
  declare siren: string;
  declare enCompte: string;
  declare typeDeCompte: string;
  declare service?: string;
  declare crmOwnerId: string;
  declare crmOwnerName: string;
  declare crmAccountId: string;
  declare crmContactId: string;
  declare booksContactId: string;
  declare encoursAut: string;
  declare encoursDispo: string;
  declare estimatesM: string;
  declare sales: string;
  declare estimates: string;
  declare orders: string;
  declare invoices: string;
  declare modeDeRGlement1?: string;
  declare compteBloqu?: string;
  declare rattachmentPartenaire?: string;
  declare dLaiDeRGlement?: string;
  declare mauvaisPayeur?: string;
  declare accountType?: string;
  declare contentieux?: string;
  declare rib?: string;
  declare etatEntreprise?: string;
  declare typeBdc?: string;
  declare bsd?: boolean;
  declare relanceFacture?: string;
  declare ppNSiretNew?: string;
  declare ppLibellFormeJuridique?: string;
  declare ppLibellCodeNaf?: string;
  declare ppEffectif?: string;
  declare ppDateDeCrAtion?: string;
  declare ppActivitPrincipaleDClarE?: string;
  declare ppApeNaf?: string;
  declare ppNTvaIntra?: string;
  declare ppDomaineDActivit?: string;
  declare booksTaxId?: string;
  declare taxPercentage?: number;
  declare createdAt: CreationOptional<Date>;
  declare updatedAt: CreationOptional<Date>;
  declare deletedAt?: CreationOptional<Date>;
}

export type ContactInput = CreationAttributes<Contact>;

Contact.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
      allowNull: false,
    },
    clientType: {
      type: DataTypes.ENUM('business', 'individual'),
    },
    name: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    siren: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    enCompte: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    typeDeCompte: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    service: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    crmOwnerId: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    crmOwnerName: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    crmAccountId: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    crmContactId: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    booksContactId: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    encoursAut: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    encoursDispo: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    estimatesM: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    sales: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    estimates: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    orders: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    invoices: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    modeDeRGlement1: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    compteBloqu: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    rattachmentPartenaire: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    dLaiDeRGlement: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    mauvaisPayeur: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    accountType: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    contentieux: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    rib: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    etatEntreprise: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    typeBdc: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    bsd: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
    },
    relanceFacture: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    ppNSiretNew: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    ppLibellFormeJuridique: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    ppLibellCodeNaf: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    ppEffectif: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    ppDateDeCrAtion: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    ppActivitPrincipaleDClarE: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    ppApeNaf: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    ppNTvaIntra: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    ppDomaineDActivit: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    booksTaxId: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    taxPercentage: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    tableName: 'contacts',
    paranoid: true,
    modelName: 'Contact',
    sequelize,
  },
);
