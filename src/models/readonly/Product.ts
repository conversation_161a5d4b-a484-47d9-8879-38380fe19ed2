import {
  sequelize,
  DataTypes,
  Model,
  InferCreationAttributes,
  InferAttributes,
  CreationOptional,
} from 'database-layer';
export class Product extends Model<InferAttributes<Product>, InferCreationAttributes<Product>> {
  declare id: CreationOptional<number>;
  declare productTypeId: number;
  declare name: string;
  declare clientName: CreationOptional<string | null>;
  declare isActive: CreationOptional<boolean>;
  declare isVisible: CreationOptional<boolean>;
  declare exclude: CreationOptional<boolean>;
  declare description: CreationOptional<string | null>;
  declare booksProductId: CreationOptional<string | null>;
  declare createdAt: CreationOptional<Date>;
  declare updatedAt: CreationOptional<Date>;
  declare deletedAt: CreationOptional<Date>;
}
Product.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
      allowNull: false,
    },
    productTypeId: {
      type: DataTypes.INTEGER,
      references: {
        model: 'product_types',
        key: 'id',
      },
    },
    name: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    clientName: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true,
    },
    isVisible: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true,
    },
    exclude: {
      allowNull: true,
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    description: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    booksProductId: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    tableName: 'products',
    paranoid: true,
    modelName: 'Product',
    sequelize,
  },
);
