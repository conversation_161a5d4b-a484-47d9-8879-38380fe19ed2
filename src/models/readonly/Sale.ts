import {
  sequelize,
  DataTypes,
  Model,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
} from 'database-layer';
export class Sale extends Model<InferAttributes<Sale>, InferCreationAttributes<Sale>> {
  declare id: CreationOptional<number>;
  declare userId: number;
  declare name?: string;
  declare phone?: string;
  declare email?: string;
  declare zohoApiClientId?: string;
  declare zohoApiSecretKey?: string;
  declare zohoApiRefreshToken?: string;
  declare zohoApiConnection?: number;
  declare booksSalespersonId?: string;
  declare booksUserId?: string;
  declare crmUserId?: string;
  declare createdAt: CreationOptional<Date>;
  declare updatedAt: CreationOptional<Date>;
  declare deletedAt: CreationOptional<Date>;
}
Sale.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
      allowNull: false,
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    phone: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    zohoApiClientId: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    zohoApiSecretKey: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    zohoApiRefreshToken: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    zohoApiConnection: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    booksSalespersonId: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    booksUserId: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    crmUserId: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    tableName: 'sales',
    timestamps: true,
    paranoid: true,
    sequelize,
    modelName: 'Sale',
  },
);
