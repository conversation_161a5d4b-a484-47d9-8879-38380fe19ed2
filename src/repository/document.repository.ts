import { IDocument } from './interfaces/document.interface';
import { DocumentDataInput } from '../services/document.service';
import { pick } from 'utils-layer';
import { DOCUMENT_UPDATE_FIELDS } from '../constant';
import dayjs from 'dayjs';
import {
  Document,
  DocumentFileUpload,
  DocumentProductLine,
  DocumentProductLinePrestation,
  DocumentProductLinePrice,
  DocumentProductLineSubOption,
  DocumentStatus,
  DocumentType,
} from '../models';
import { Op, Repository, sequelize, QueryTypes } from 'database-layer';
import { DOCUMENT_STATUS } from '../utils';

export default class DocumentRepository extends Repository<Document> implements IDocument {
  constructor() {
    super(Document);
  }

  /**
   * Parse the fullSearch string into search conditions
   * @param fullSearch - The full search string
   * @returns Object containing the where conditions for the query
   */
  private parseFullSearchConditions(fullSearch: string) {
    if (!fullSearch || fullSearch.length === 0) return null;

    const parts = fullSearch.split('/');
    const day = parseInt(parts[0]);
    const month = parseInt(parts[1]);
    const year = parseInt(parts[2]);
    const date = dayjs()
      .set('year', year)
      .set('month', month - 1)
      .set('date', day)
      .startOf('day');

    return {
      [Op.or]: [
        date.isValid() ? { createdAt: { [Op.gte]: date.toDate(), [Op.lt]: date.add(1, 'day').toDate() } } : null,
        { cdeZoho: { [Op.like]: `%${fullSearch}%` } },
        sequelize.where(sequelize.literal("concat(Document.siteAddressPostalCode, ' - ', Document.siteAddressCity)"), {
          [Op.like]: `%${fullSearch}%`,
        }),
        { '$Contact.name$': { [Op.like]: `%${fullSearch}%` } },
        { '$Vendeur.name$': { [Op.like]: `%${fullSearch}%` } },
      ].filter(Boolean),
    };
  }

  /**
   * Retrieve all product lines related to a list of document IDs
   * @param documentIds - List of document IDs
   * @returns Promise containing the list of DocumentProductLines
   */
  private async getAllProductLinesByDocumentIds(documentIds: number[]) {
    return DocumentProductLine.findAll({
      where: {
        documentId: { [Op.in]: documentIds },
      },
      include: [
        {
          model: DocumentProductLinePrestation,
          as: 'DocumentProductLinePrestations',
          required: false,
        },
      ],
    });
  }

  /**
   * Combine product lines into each document and return the list
   * @param documents - List of documents
   * @param allProductLines - List of product lines
   * @returns List of documents with product lines included
   */
  private combineDocumentsWithProductLines(documents: Document[], allProductLines: DocumentProductLine[]) {
    return documents.map((doc) => {
      const productLines = allProductLines.filter((line) => line.documentId === doc.id);
      return {
        ...doc.toJSON(),
        DocumentProductLines: productLines,
      };
    });
  }

  /**
   * Retrieve a list of documents with filters
   * @param limit - Number of documents per page
   * @param page - Current page number
   * @param fullSearch - Full search string
   * @returns List of documents and total count
   */
  public async getDocumentsFilterQuery(
    limit: string,
    page: string,
    orderBy: string,
    fullSearch: string,
    filters: object,
  ) {
    console.log('orderBy: ', orderBy);
    const _limit = limit ? Number(limit) : 10;
    const _page = page ? Number(page) : 1;

    // Generate where conditions from fullSearch
    const whereConditions = this.parseFullSearchConditions(fullSearch);

    if (filters['createdAt[gte]'] && filters['createdAt[lte]']) {
      const createdAtFrom = new Date(filters['createdAt[gte]']);
      const createdAtTo = new Date(filters['createdAt[lte]']);
      delete filters['createdAt[gte]'];
      delete filters['createdAt[lte]'];
      filters = {
        ...filters,
        createdAt: { [Op.between]: [createdAtFrom, createdAtTo] },
      };
    }
    // Check not null
    let whereInterventionId = { interventionId: { [Op.ne]: null } };
    if (filters['interventionId']) {
      // Where interventionId
      whereInterventionId = { interventionId: filters['interventionId'] };
      delete filters['interventionId'];
    }
    let isFilterID = false;
    // Check filters ids
    if (filters['ids[]']) {
      isFilterID = true;
      // Get ids
      const ids = JSON.parse(filters['ids[]']);
      // Delete ids from filters
      delete filters['ids[]'];
      filters = {
        ...filters,
        id: { [Op.in]: ids },
      };
    }

    let whereCondition;

    if (!filters['documentStatusId'] || isFilterID) {
      if ((fullSearch && fullSearch.length > 0) || isFilterID) {
        whereCondition = {
          status: { [Op.or]: [DOCUMENT_STATUS.TO_PROCESS, DOCUMENT_STATUS.CONSIDERED, DOCUMENT_STATUS.PROCESSED] },
        };
      } else {
        whereCondition = { status: DOCUMENT_STATUS.TO_PROCESS };
      }
    }

    // Configure query options
    const options = {
      where: {
        ...whereConditions,
        ...filters,
      },
      include: [
        {
          model: DocumentStatus,
          as: 'DocumentStatus',
          where: whereCondition,
          required: true,
        },
        {
          model: DocumentType,
          as: 'DocumentType',
          required: true,
        },
        {
          model: Document,
          as: 'EstimateDocument',
        },
        // {
        //   model: Contact,
        //   as: 'Contact',
        //   required: true,
        // },
        // {
        //   model: Sale,
        //   as: 'Vendeur',
        //   required: true,
        // },
        // {
        //   model: Sale,
        //   as: 'Referent',
        //   required: true,
        // },
        {
          model: DocumentFileUpload,
          as: 'DocumentFileUploads',
          required: false,
        },
        {
          model: DocumentProductLine,
          as: 'DocumentProductLines',
          required: true,
          where: whereInterventionId,
          include: [
            {
              model: DocumentProductLinePrestation,
              as: 'DocumentProductLinePrestations',
              required: false,
            },
            {
              model: DocumentProductLinePrice,
              as: 'DocumentProductLinePrices',
              required: false,
            },
            {
              model: DocumentProductLineSubOption,
              as: 'DocumentProductLineSubOptions',
              required: false,
            },
          ],
        },
      ],
    };

    let ids = [];
    // Check fullSearch
    if (fullSearch && fullSearch.length > 0) {
      // Retrieve the list of document ids
      ids = (
        await Document.findAll({
          ...options,
        })
      ).map((doc) => doc.id);
    }

    // Retrieve the list of documents
    const rs = await Document.findAndCountAll({
      where: fullSearch && fullSearch.length > 0 ? { id: { [Op.in]: ids } } : options.where,
      include: options.include,
      attributes: {
        include: [
          [
            sequelize.literal("concat(Document.siteAddressPostalCode, ' - ', Document.siteAddressCity)"),
            'siteAddressPostalCodeAndCity',
          ],
        ],
      },
      offset: (_page - 1) * _limit,
      distinct: true,
      limit: _limit,
      order: [['createdAt', 'DESC']],
    });
    const documents = rs.rows;
    const count = rs.count;
    // Retrieve all DocumentProductLines related to the documents
    const documentIds = documents.map((doc) => doc.id);
    // Retrieve all DocumentProductLines
    const allProductLines = await this.getAllProductLinesByDocumentIds(documentIds);

    // Combine product lines into documents
    const documentsWithAllProductLines = this.combineDocumentsWithProductLines(documents, allProductLines);

    return { rows: documentsWithAllProductLines, count: count };
  }

  /**
   * Get the document after saving for the create or update document step function
   * @param document - The document to get
   * @param documentInput - The document input
   * @param isUpdate - Whether the document is an update
   * @returns The document after saving
   */
  async getDocumentAfterSaving(
    document: Document,
    documentInput: DocumentDataInput,
    isUpdate = false,
  ): Promise<[Document, { [key: string]: unknown }]> {
    const lineItems = documentInput.lineItems;
    const savedDocument = await new DocumentRepository().findById(document.id, {
      include: [
        'DocumentCCContacts',
        'DocumentCCLibresContacts',
        'DocumentStatus',
        'DocumentFileUploads',
        {
          model: DocumentProductLine,
          include: [
            'DocumentProductLinePrices',
            'DocumentProductLineSubOptions',
            {
              model: DocumentProductLinePrestation,
              include: ['DocumentProductLinePrestationSubOptions'],
            },
          ],
        },
      ],
    });

    const extraData = pick(
      isUpdate ? DOCUMENT_UPDATE_FIELDS.UPDATE_DOCUMENT_EXTRA_DATA : DOCUMENT_UPDATE_FIELDS.CREATE_DOCUMENT_EXTRA_DATA,
      documentInput,
    );
    extraData.DocumentProductLines = savedDocument.DocumentProductLines.map((productLine) => {
      const selectedProduct = lineItems.find((item) => item.productId === productLine.productId);
      return {
        ...productLine.toJSON(),
        name: selectedProduct?.name,
        booksProductId: selectedProduct?.booksProductId,
      };
    });
    extraData.Demander = documentInput.valueDemander;
    return [savedDocument, extraData];
  }

  /**
   * Get provider invoice data with expeditions and product lines
   * @param serviceProviderId - The service provider ID to filter by
   * @returns Promise containing the provider invoice data
   */
  public async getProviderInvoiceData(serviceProviderId: number) {
    const query = `
      SELECT
        documents.id AS documentId,
        expeditions.id AS expeditionId,
        expeditions.siteAddress,
        expeditions.cdeZoho,
        prestation_pose.prestationDate AS datePose,
        prestation_enlevement.prestationDate AS dateEnlevement,
        products.name AS productName,
        document_product_lines.id,
        document_product_lines.productNameForClient,
        document_product_lines.unitPrice,
        document_product_lines.quantity,
        documentLineClient.isTransport,
        documentLineClient.totalPriceClient
      FROM documents
      INNER JOIN expeditions ON expeditions.documentId = documents.id
      INNER JOIN document_types ON document_types.id = documents.documentTypeId
                                AND document_types.key = "service_provider_order"
      INNER JOIN products ON products.id = expeditions.productId
      INNER JOIN document_product_lines ON document_product_lines.documentId = documents.id
      LEFT JOIN (
        SELECT
          expedition_prestations.expeditionId,
          expedition_prestations.prestationDate
        FROM expedition_prestations
        INNER JOIN prestation_types ON prestation_types.id = expedition_prestations.prestationTypeId
                                    AND prestation_types.key IN ("POSE","ROTATION_POSE","CHARGEMENT_EXPRESS")
      ) AS prestation_pose ON prestation_pose.expeditionId = expeditions.id
      LEFT JOIN (
        SELECT
          expedition_prestations.expeditionId,
          expedition_prestations.prestationDate
        FROM expedition_prestations
        INNER JOIN prestation_types ON prestation_types.id = expedition_prestations.prestationTypeId
                                    AND prestation_types.key IN ("ENLEVEMENT","ROTATION_ENLEVEMENT","CHARGEMENT_EXPRESS","ENLEVEMENT_DEFINITIF")
      ) AS prestation_enlevement ON prestation_enlevement.expeditionId = expeditions.id
      LEFT JOIN (
        SELECT
          document_product_line_connections.derivedProductLineId AS "documentLineSPId",
          document_product_lines.id,
          documents.cdeZoho,
          products.isTransport,
          CASE
            WHEN products.isTransport = 1 THEN document_product_lines.unitPrice
            ELSE SUM(document_product_lines.quantity * document_product_lines.unitPrice)
          END AS totalPriceClient
        FROM documents
        INNER JOIN document_types ON document_types.id = documents.documentTypeId
                                  AND document_types.key = "order"
        INNER JOIN document_product_lines ON document_product_lines.documentId = documents.id
        INNER JOIN products ON products.id = document_product_lines.productId
        INNER JOIN document_product_line_connections ON document_product_line_connections.masterProductLineId = document_product_lines.id
        GROUP BY document_product_line_connections.derivedProductLineId, document_product_lines.id, documents.cdeZoho, products.isTransport
      ) AS documentLineClient ON documentLineClient.documentLineSPId = document_product_lines.id
      WHERE documents.serviceProviderId = :serviceProviderId
    `;

    const results = await sequelize.query(query, {
      replacements: { serviceProviderId },
      type: QueryTypes.SELECT,
    });

    return results;
  }
}
