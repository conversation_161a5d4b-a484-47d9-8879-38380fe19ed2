import { publishDatabaseChangeEvent, Repository, Op } from 'database-layer';
import {
  DocumentProductLine,
  DocumentProductLineAttributes,
  DocumentProductLineInput,
  DocumentProductLinePrestation,
  DocumentProductLinePrestationAttributes,
  DocumentProductLinePrestationSubOption,
  DocumentProductLinePrestationSubOptionAttributes,
  DocumentProductLinePrice,
  DocumentProductLinePriceInput,
  DocumentProductLineSubOption,
  DocumentProductLineSubOptionAttributes,
} from '../models';
import { IDocumentProductLine } from './interfaces/document-product-line.interface';
import { omit, pick } from 'utils-layer';
import DocumentProductLinePrestationRepository from './document_product_line_prestation.repository';
import DocumentProductLinePrestationSubOptionRepository from './document_product_line_prestation_sub_option.repository';
import DocumentProductLineSubOptionRepository from './document-product-line-sub-option.repository';
import DocumentProductLinePriceRepository from './document_product_line_prices.repository';
import { DOCUMENT_UPDATE_FIELDS } from '../constant';

export default class DocumentProductLineRepository
  extends Repository<DocumentProductLine>
  implements IDocumentProductLine
{
  constructor() {
    super(DocumentProductLine);
  }

  /**
   * Create the product lines, prestation date lines, product line sub options and product line prices
   * @param items - The product lines to create
   * @param documentId - The id of the document
   */
  async createDocumentProductLines(
    items: (DocumentProductLineInput & {
      prestationDateLine?: DocumentProductLinePrestation | DocumentProductLinePrestationSubOption[];
      productLinePrices?: DocumentProductLinePrice[];
      productLineSubOptions?: DocumentProductLineSubOption[];
      priceFamilyValue?: string | number;
      name?: string;
      booksProductId?: string;
    })[],
    documentId: number,
  ): Promise<void> {
    // pick the DocumentProductLineAttributes and some extra fields
    const inputProductLines = items.map((lineItem) => ({
      ...pick([...DocumentProductLineAttributes, ...DOCUMENT_UPDATE_FIELDS.DOCUMENT_PRODUCT_LINE_EXTRA_CREATE_DATA], {
        ...lineItem,
      }),
      documentId,
    }));
    // create the product lines
    const productLines = await new DocumentProductLineRepository().bulkCreate(inputProductLines);
    // send the database change event (Ticket QBO-1478)
    const increaseProductsIds = [];
    productLines.forEach((productLine) => {
      if (productLine.productId) {
        increaseProductsIds.push(productLine.productId);
      }
    });
    if (increaseProductsIds.length > 0) {
      await this.createDatabaseChangeEvent(increaseProductsIds, productLines[0], 'CREATE');
    }
    // create the prestation date lines, product line sub options and product line prices for each product line
    const result = productLines.map(async (productLine, index) => {
      // prepare the DocumentProductLinePrestation data for create
      const line = inputProductLines[index]?.prestationDateLine?.map((item) => ({
        ...pick(
          DocumentProductLinePrestationAttributes.filter((i) => i !== 'id'),
          item,
        ),
        documentProductLineId: productLine.id,
      }));
      // prepare the DocumentProductLineSubOption data for create
      const productLineSubOption = inputProductLines[index]?.productLineSubOptions?.map((item) => ({
        ...pick(
          DocumentProductLineSubOptionAttributes.filter((i) => i !== 'id'),
          item,
        ),
        documentProductLineId: productLine.id,
      }));
      // prepare the DocumentProductLinePrice data for create
      const productLinePrices = this._prepareProductLinePrices(
        {
          productLineId: productLine.id,
          inputPrices: inputProductLines[index]?.productLinePrices,
          priceFamilyValue: inputProductLines[index]?.priceFamilyValue,
          priceFamilyBuyingPrice: inputProductLines[index]?.priceFamilyBuyingPrice,
          priceFamilyPriceMargin: inputProductLines[index]?.priceFamilyPriceMargin,
          priceFamilyValidSP: inputProductLines[index]?.priceFamilyValidSP,
          linePriceFamilyId: inputProductLines[index]?.linePriceFamilyId,
        },
        'create',
      );
      // create the prestation date lines
      const prestationDateLine = await new DocumentProductLinePrestationRepository().bulkCreate(line);
      // prepare the DocumentProductLinePrestationSubOption data for create
      const prestationDateLineSubOption =
        inputProductLines[index]?.prestationDateLine?.map((item, prestationDateLineItemIndex) => ({
          ...pick(
            DocumentProductLinePrestationSubOptionAttributes.filter((i) => i !== 'id'),
            item,
          ),
          documentProductLinePrestationId: prestationDateLine[prestationDateLineItemIndex].id,
        })) || [];
      // create the prestation date line sub options, product line sub options and product line prices
      await Promise.all([
        new DocumentProductLinePrestationSubOptionRepository().bulkCreate(prestationDateLineSubOption),
        new DocumentProductLineSubOptionRepository().bulkCreate(productLineSubOption),
        new DocumentProductLinePriceRepository().bulkCreate(productLinePrices),
      ]);
    });

    //handle multi-product
    const listMultiProduct = inputProductLines?.filter((item) => item?.mainProductId && item?.headerName);
    const listLineMultiProductUpdate = [];
    listMultiProduct?.map((item) => {
      const lineMultiProduct = productLines?.find(
        (i) =>
          i?.headerName === item?.headerName &&
          i?.mainProductId === item?.mainProductId &&
          i?.lineOrderNumber === item?.lineOrderNumber,
      );
      if (lineMultiProduct) {
        const subMultiProduct = inputProductLines?.filter((i) => i?.parentKey === item?.parentKey);
        subMultiProduct?.map((sp) => {
          const lineSubMultiProduct = productLines?.find(
            (ip) => ip?.mainProductId === sp?.mainProductId && ip?.lineOrderNumber === sp?.lineOrderNumber,
          );
          if (lineSubMultiProduct) {
            listLineMultiProductUpdate?.push({
              ...lineSubMultiProduct,
              id: lineSubMultiProduct.id,
              parentProductLineId: lineMultiProduct?.id,
            });
          }
        });
      }
    });
    await Promise.all([result]);
    await Promise.all(
      listLineMultiProductUpdate.map(async (item) => {
        await new DocumentProductLineRepository().update(item.id, item);
      }),
    );
  }

  async updateDocumentProductLines(
    items: (DocumentProductLineInput & {
      prestationDateLine?: DocumentProductLinePrestation | DocumentProductLinePrestationSubOption[];
    })[],
    documentId: number,
  ): Promise<void> {
    const documentProductLinePrestationRepo = new DocumentProductLinePrestationRepository();
    const documentProductLinePrestationSubOptionRepo = new DocumentProductLinePrestationSubOptionRepository();
    const documentProductLineSubOptionRepo = new DocumentProductLineSubOptionRepository();
    const documentProductLinePriceRepo = new DocumentProductLinePriceRepository();
    // pick the DocumentProductLineAttributes and some extra fields
    const inputLineItems = items.map((lineItem) => ({
      ...pick([...DocumentProductLineAttributes, ...DOCUMENT_UPDATE_FIELDS.DOCUMENT_PRODUCT_LINE_EXTRA_UPDATE_DATA], {
        ...lineItem,
      }),
      documentId,
    }));
    const prestationDateLinesCreateData = [];
    const prestationDateLinesUpdateData = [];
    let productLinePricesUpdateData = [];
    let prestationDateLineSubOptionsCreateData = [];
    const prestationDateLineSubOptionsUpdateData = [];
    const productLinePricesCreateData = [];
    let productLineSubOptionsCreateData = [];
    const productLineSubOptionsUpdateData = [];
    // get the product lines to update
    const productLinesToUpdate = inputLineItems.filter((productLine) => productLine.id);
    // get the product lines to create
    const productLinesToCreate = inputLineItems.filter((productLine) => !productLine.id);
    // pick the DocumentProductLineAttributes for update
    const updateProductLinesData = productLinesToUpdate.map((productLine) => ({
      ...pick(DocumentProductLineAttributes, productLine),
    }));
    // pick the DocumentProductLineAttributes for create
    const createProductLinesData = productLinesToCreate.map((productLine) => ({
      ...pick(DocumentProductLineAttributes, productLine),
      booksProductLineHeaderId: productLine?.headerName ? productLine?.booksProductLineHeaderId : null,
    }));
    // prepare the product line prices data for update
    productLinesToUpdate?.forEach((productLine) => {
      productLinePricesUpdateData = productLinePricesUpdateData.concat(
        this._prepareProductLinePrices(
          {
            productLineId: productLine.id,
            inputPrices: productLine?.productLinePrices,
            priceFamilyValue: productLine?.priceFamilyValue,
            priceFamilyBuyingPrice: productLine?.priceFamilyBuyingPrice,
            priceFamilyPriceMargin: productLine?.priceFamilyPriceMargin,
            priceFamilyValidSP: productLine?.priceFamilyValidSP,
            linePriceFamilyId: productLine?.linePriceFamilyId,
          },
          'update',
        ),
      );
      productLine?.prestationDateLine?.forEach((prestationDateLine) => {
        if (prestationDateLine?.id) {
          prestationDateLinesUpdateData.push({
            ...pick(DocumentProductLinePrestationAttributes, prestationDateLine),
            documentProductLineId: productLine.id,
          });
          prestationDateLineSubOptionsUpdateData.push({
            ...pick(DocumentProductLinePrestationSubOptionAttributes, prestationDateLine),
            documentProductLinePrestationId: prestationDateLine?.id,
          });
        } else {
          prestationDateLinesCreateData.push({
            ...pick(DocumentProductLinePrestationAttributes, prestationDateLine),
            documentProductLineId: productLine.id,
          });
          prestationDateLineSubOptionsCreateData.push({
            ...pick(DocumentProductLinePrestationSubOptionAttributes, prestationDateLine),
          });
        }
      });
      productLine?.productLineSubOptions?.forEach((productLineSubOption) => {
        if (productLineSubOption?.id) {
          productLineSubOptionsUpdateData?.push({
            ...pick(DocumentProductLineSubOptionAttributes, productLineSubOption),
            documentProductLineId: productLine.id,
          });
        } else {
          productLineSubOptionsCreateData?.push({
            ...pick(DocumentProductLineSubOptionAttributes, productLineSubOption),
            documentProductLineId: productLine.id,
          });
        }
      });
    });
    await Promise.all([
      documentProductLinePrestationRepo.bulkUpdate(prestationDateLinesUpdateData, {
        updateOnDuplicate: DOCUMENT_UPDATE_FIELDS.DOCUMENT_PRODUCT_LINE_PRESTATION,
      }),
      documentProductLinePrestationRepo.delete({
        where: {
          documentProductLineId: {
            [Op.in]: updateProductLinesData.map((productLine) => productLine.id),
          },
          id: {
            [Op.notIn]: prestationDateLinesUpdateData.map((prestationDateLine) => prestationDateLine.id),
          },
        },
      }),
    ]);
    if (prestationDateLinesCreateData.length > 0) {
      const prestationDateLine = await documentProductLinePrestationRepo.bulkCreate(prestationDateLinesCreateData);
      const prestationDateLineSubOption =
        prestationDateLineSubOptionsCreateData?.map((item, index) => ({
          ...item,
          documentProductLinePrestationId: prestationDateLine[index].id,
        })) || [];
      prestationDateLineSubOptionsCreateData = prestationDateLineSubOption;
    }
    const documentProductLineRepo = new DocumentProductLineRepository();
    // send the database change event (Ticket QBO-1478)
    // Convert to Map for quick lookup
    const newProductsMap = new Map(createProductLinesData.map((item) => [item.productId, item]));
    const productsToIncrement = [];
    const productsToDecrement = [];
    // Incrementing the products from new products lines
    newProductsMap.forEach((newProduct) => {
      productsToIncrement.push(newProduct.mainProductId ? newProduct.mainProductId : newProduct.productId);
      // Only increment the main product if it's a sub product
    });
    updateProductLinesData.forEach(async (updatedProduct) => {
      const previousProduct = await new DocumentProductLineRepository().findById(updatedProduct.id);
      // If the productId has changed, update both lists (decrement old product, increment new product)
      if (previousProduct && previousProduct.productId !== updatedProduct.productId) {
        productsToDecrement.push(
          previousProduct.mainProductId ? previousProduct.mainProductId : previousProduct.productId,
        );
        // Only deccrement the main product if it's a sub product
        productsToIncrement.push(
          updatedProduct.mainProductId ? updatedProduct.mainProductId : updatedProduct.productId,
        );
        // Only increment the main product if it's a sub product
      }
    });
    // Decrementing the products from old products lines
    const productsToDelete = await new DocumentProductLineRepository().find({
      where: {
        documentId,
        id: {
          [Op.notIn]: updateProductLinesData.map((productLine) => productLine.id),
        },
      },
    });
    productsToDelete.forEach((product) => {
      productsToDecrement.push(product.mainProductId ? product.mainProductId : product.productId);
      // Only increment the main product if it's a sub product
    });
    // Creating database change events for incrementing and decrementing products
    if (productsToIncrement.length > 0) {
      await this.createDatabaseChangeEvent(productsToIncrement, new DocumentProductLine(), 'CREATE');
    }
    if (productsToDecrement.length > 0) {
      await this.createDatabaseChangeEvent(productsToDecrement, new DocumentProductLine(), 'DELETE');
    }
    await Promise.all([
      documentProductLinePrestationSubOptionRepo.bulkCreate(prestationDateLineSubOptionsCreateData),
      documentProductLineRepo.bulkUpdate(updateProductLinesData, {
        updateOnDuplicate: DOCUMENT_UPDATE_FIELDS.DOCUMENT_PRODUCT_LINES,
      }),
      documentProductLineRepo.delete({
        where: {
          documentId,
          id: {
            [Op.notIn]: productLinesToUpdate.map((productLine) => productLine.id),
          },
        },
      }),
    ]);
    prestationDateLineSubOptionsCreateData = [];
    const productLines = await documentProductLineRepo.bulkCreate(createProductLinesData);
    const result = productLines?.map(async (productLine, index) => {
      const line = productLinesToCreate[index]?.prestationDateLine?.map((item) => ({
        ...omit(item, 'id'),
        documentProductLineId: productLine.id,
      }));
      const productLineSubOption = productLinesToCreate[index]?.productLineSubOptions?.map((item) => ({
        ...item,
        documentProductLineId: productLine.id,
      }));
      if (productLinesToCreate[index]?.priceFamilyValue || productLinesToCreate[index]?.priceFamilyValue === 0) {
        productLinePricesCreateData.push({
          documentProductLineId: productLine.id,
          value: productLinesToCreate[index]?.priceFamilyValue,
          buyingPrice: productLinesToCreate[index]?.priceFamilyBuyingPrice,
          priceMargin: productLinesToCreate[index]?.priceFamilyPriceMargin,
          validSP: productLinesToCreate[index]?.priceFamilyValidSP,
        });
      }
      productLinesToCreate[index]?.productLinePrices?.forEach((item) => {
        const result: DocumentProductLinePriceInput = {
          documentProductLineId: productLine.id,
          priceId: item?.priceId,
        };
        if (item?.priceOptionValue || item?.priceOptionValue === 0) {
          productLinePricesCreateData.push({
            ...result,
            value: item?.priceOptionValue,
            priceOptionId: item?.priceOptionId,
            priceOptionLabel: item?.priceOptionLabel,
          });
        }
        if (item?.priceValue || item?.priceValue === 0) {
          productLinePricesCreateData.push({
            ...result,
            value: item?.priceValue,
            priceLabel: item?.priceLabel,
          });
        }
        if (item?.priceSubOptionId) {
          productLinePricesCreateData.push({
            ...result,
            priceOptionId: item?.priceOptionId,
            priceOptionLabel: item?.priceOptionLabel,
            priceSubOptionId: item?.priceSubOptionId,
            priceSubOptionLabel: item?.priceSubOptionLabel,
          });
        }
      });
      productLineSubOptionsCreateData = productLineSubOptionsCreateData.concat(productLineSubOption);
      const prestationDateLine = await documentProductLinePrestationRepo.bulkCreate(line);
      const prestationDateLineSubOption =
        productLinesToCreate[index]?.prestationDateLine?.map((item, prestationDateLineItemIndex) => ({
          ...pick(DocumentProductLinePrestationSubOptionAttributes, item),
          documentProductLinePrestationId: prestationDateLine[prestationDateLineItemIndex].id,
        })) || [];
      prestationDateLineSubOptionsCreateData =
        prestationDateLineSubOptionsCreateData.concat(prestationDateLineSubOption);
    });
    await Promise.all(result);
    await Promise.all([
      documentProductLinePrestationSubOptionRepo.delete({
        where: {
          documentProductLinePrestationId: {
            [Op.in]: prestationDateLinesUpdateData.map((item) => item.id),
          },
          id: {
            [Op.notIn]: prestationDateLineSubOptionsUpdateData.map(
              (prestationDateLineSubOption) => prestationDateLineSubOption.id,
            ),
          },
        },
      }),
      documentProductLinePrestationSubOptionRepo.bulkUpdate(prestationDateLineSubOptionsUpdateData, {
        updateOnDuplicate: DOCUMENT_UPDATE_FIELDS.DOCUMENT_PRODUCT_LINE_PRESTATION_SUB_OPTION,
      }),
      documentProductLinePrestationSubOptionRepo.bulkCreate(prestationDateLineSubOptionsCreateData),
      documentProductLineSubOptionRepo.delete({
        where: {
          documentProductLineId: {
            [Op.in]: updateProductLinesData.map((productLine) => productLine.id),
          },
          id: {
            [Op.notIn]: productLineSubOptionsUpdateData.map((productLineSubOption) => productLineSubOption.id),
          },
        },
      }),
      documentProductLineSubOptionRepo.bulkUpdate(productLineSubOptionsUpdateData, {
        updateOnDuplicate: DOCUMENT_UPDATE_FIELDS.DOCUMENT_PRODUCT_LINE_SUB_OPTION,
      }),
      documentProductLineSubOptionRepo.bulkCreate(productLineSubOptionsCreateData),
      documentProductLinePriceRepo.delete({
        where: {
          documentProductLineId: {
            [Op.in]: updateProductLinesData.map((productLine) => productLine.id),
          },
          id: {
            [Op.notIn]: productLinePricesUpdateData.map((productLinePrice) => productLinePrice.id),
          },
        },
      }),
      documentProductLinePriceRepo.bulkUpdate(productLinePricesUpdateData, {
        updateOnDuplicate: DOCUMENT_UPDATE_FIELDS.DOCUMENT_PRODUCT_LINE_PRICE,
      }),
      documentProductLinePriceRepo.bulkCreate(productLinePricesCreateData),
    ]);
    //handle create line multi-product
    const listMultiProduct = productLinesToCreate?.filter((item) => item?.mainProductId && item?.headerName);
    const listLineMultiProductUpdate = [];
    listMultiProduct?.map((item) => {
      const lineMultiProduct = productLines?.find(
        (i) =>
          i?.headerName === item?.headerName &&
          i?.mainProductId === item?.mainProductId &&
          i?.lineOrderNumber === item?.lineOrderNumber &&
          !i?.parentProductLineId,
      );
      if (lineMultiProduct) {
        const subMultiProduct = productLinesToCreate?.filter((i) => i?.parentKey === item?.parentKey);
        subMultiProduct?.map((sp) => {
          const lineSubMultiProduct = productLines?.find(
            (ip) => ip?.mainProductId === sp?.mainProductId && ip?.lineOrderNumber === sp?.lineOrderNumber,
          );
          if (lineSubMultiProduct) {
            listLineMultiProductUpdate?.push({
              ...lineSubMultiProduct,
              id: lineSubMultiProduct.id,
              parentProductLineId: lineMultiProduct?.id,
            });
          }
        });
      }
    });
    if (listLineMultiProductUpdate?.length > 0) {
      await Promise.all(
        listLineMultiProductUpdate.map(async (item) => {
          await new DocumentProductLineRepository().update(item.id, item);
        }),
      );
    }
  }

  private _prepareProductLinePrices(
    data: {
      productLineId: number;
      inputPrices: (DocumentProductLinePriceInput & {
        priceOptionValue?: number;
        priceValue?: number;
      })[];
      priceFamilyValue?: number;
      priceFamilyBuyingPrice?: number;
      priceFamilyPriceMargin?: number;
      priceFamilyValidSP?: number;
      linePriceFamilyId?: number;
    },
    actionType: 'create' | 'update' = 'create',
  ) {
    const {
      productLineId,
      inputPrices,
      priceFamilyValue,
      priceFamilyBuyingPrice,
      priceFamilyPriceMargin,
      priceFamilyValidSP,
      linePriceFamilyId,
    } = data;
    const productLinePrices = [];
    if (priceFamilyValue || priceFamilyValue === 0) {
      productLinePrices.push({
        documentProductLineId: productLineId,
        value: priceFamilyValue,
        buyingPrice: priceFamilyBuyingPrice,
        priceMargin: priceFamilyPriceMargin,
        validSP: priceFamilyValidSP,
        ...(actionType === 'update' && linePriceFamilyId ? { id: linePriceFamilyId } : {}),
      });
    }
    inputPrices?.forEach((item) => {
      const result: DocumentProductLinePriceInput = {
        documentProductLineId: productLineId,
        priceId: item?.priceId,
      };
      if (item?.priceOptionValue || item?.priceOptionValue === 0) {
        productLinePrices.push({
          ...result,
          ...(actionType === 'update' && item.id ? { id: item.id } : {}),
          value: item?.priceOptionValue,
          priceOptionId: item?.priceOptionId,
          priceOptionLabel: item?.priceOptionLabel,
        });
      }
      if (item?.priceValue || item?.priceValue === 0) {
        productLinePrices.push({
          ...result,
          ...(actionType === 'update' && item.id ? { id: item.id } : {}),
          value: item?.priceValue,
          buyingPrice: item?.buyingPrice,
          priceMargin: item?.priceMargin,
          validSP: item?.validSP,
          priceLabel: item?.priceLabel,
        });
      } else if (item?.priceSubOptionId) {
        productLinePrices.push({
          ...result,
          ...(actionType === 'update' && item.id ? { id: item.id } : {}),
          priceOptionId: item?.priceOptionId,
          priceOptionLabel: item?.priceOptionLabel,
          priceSubOptionId: item?.priceSubOptionId,
          priceSubOptionLabel: item?.priceSubOptionLabel,
        });
      }
    });
    return productLinePrices;
  }
  /**
   * Create a database change event (Ticket QBO-1478)
   * @param productIds - The product ids
   * @param productLine - The product line
   * @param action - The action
   */
  private createDatabaseChangeEvent = async (
    productIds: string[],
    productLine: DocumentProductLine,
    action: 'CREATE' | 'DELETE',
  ) => {
    const record: Record<string, string> = Object.fromEntries(
      productIds.map((value, index) => [`key${index + 1}`, value]),
    );
    console.log(record);
    try {
      await publishDatabaseChangeEvent({
        action,
        data: productLine,
        extraData: {
          record,
        },
      });
    } catch (error) {
      console.error(error);
      // Return an ErrorResponse with the error message and the error object.
      throw error;
    }
  };
}
