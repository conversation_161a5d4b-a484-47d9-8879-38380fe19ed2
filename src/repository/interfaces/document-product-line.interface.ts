import {
  DocumentProductLineInput,
  DocumentProductLinePrestation,
  DocumentProductLinePrestationSubOption,
  DocumentProductLinePrice,
  DocumentProductLineSubOption,
} from '../../models';

export type DocumentProductLineItem = DocumentProductLineInput & {
  prestationDateLine?: DocumentProductLinePrestation | DocumentProductLinePrestationSubOption[];
  productLinePrices?: DocumentProductLinePrice[];
  productLineSubOptions?: DocumentProductLineSubOption[];
  priceFamilyValue?: string | number;
  name?: string;
  booksProductId?: string;
};
export interface IDocumentProductLine {
  createDocumentProductLines(items: DocumentProductLineItem[], documentId: number): Promise<void>;
  updateDocumentProductLines(items: DocumentProductLineItem[], documentId: number): Promise<void>;
}
