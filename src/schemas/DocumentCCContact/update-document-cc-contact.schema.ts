import { transpileSchema } from 'middy-layer';

const updateDocumentCCContactSchema = {
  type: 'object',
  required: ['body'],
  properties: {
    body: {
      type: 'object',
      required: ['documentId', 'contactPersonId'],
      properties: {
        documentId: { $ref: '#/definitions/integer' },
        contactPersonId: { $ref: '#/definitions/integer' },
      },
    },
  },
  definitions: {
    integer: {
      type: 'integer',
      minimum: 1,
    },
  },
};

export default transpileSchema(updateDocumentCCContactSchema);
