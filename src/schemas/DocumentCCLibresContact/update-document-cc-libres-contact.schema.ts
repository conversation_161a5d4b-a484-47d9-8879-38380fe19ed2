import { transpileSchema } from 'middy-layer';

const updateDocumentCCLibresContactSchema = {
  type: 'object',
  required: ['body'],
  properties: {
    body: {
      type: 'object',
      required: ['documentId'],
      properties: {
        documentId: { $ref: '#/definitions/integer' },
        email: { $ref: '#/definitions/nullable-string' },
      },
    },
  },
  definitions: {
    integer: {
      type: 'integer',
      minimum: 1,
    },
    'nullable-string': {
      type: ['string', 'null'],
    },
  },
};

export default transpileSchema(updateDocumentCCLibresContactSchema);
