import { transpileSchema } from 'middy-layer';

const createDocumentFileUploadsSchema = {
  type: 'object',
  required: ['body'],
  properties: {
    body: {
      type: 'object',
      required: ['documentId', 'name', 'url', 'type'],
      properties: {
        documentId: { type: 'number' },
        name: { type: 'string' },
        url: { type: 'string' },
        type: { type: 'string' },
      },
    },
  },
};

export default transpileSchema(createDocumentFileUploadsSchema);
