import { transpileSchema } from 'middy-layer';

const createDocumentProductLineSchema = {
  type: 'object',
  required: ['body'],
  properties: {
    body: {
      type: 'object',
      required: ['documentId', 'productId', 'priceFamilyId', 'productNameForClient'],
      properties: {
        documentId: { $ref: '#/definitions/integer' },
        productId: { $ref: '#/definitions/integer' },
        priceFamilyId: { $ref: '#/definitions/integer' },
        productNameForClient: { $ref: '#/definitions/nullable-string' },
      },
    },
  },
  definitions: {
    integer: {
      type: 'integer',
      minimum: 1,
    },
    'nullable-string': {
      type: ['string', 'null'],
    },
  },
};

export default transpileSchema(createDocumentProductLineSchema);
