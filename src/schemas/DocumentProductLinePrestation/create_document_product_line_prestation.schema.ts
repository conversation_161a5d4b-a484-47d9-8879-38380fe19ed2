import { transpileSchema } from 'middy-layer';

const createDocumentProductLinePrestationSchema = {
  type: 'object',
  required: ['body'],
  properties: {
    body: {
      type: 'object',
      required: ['documentProductLineId', 'prestationDate', 'documentProductLinePrestationStatusId'],
      properties: {
        documentProductLineId: { $ref: '#/definitions/integer' },
        prestationDate: { $ref: '#/definitions/non-empty-string' },
        booksPackageId: { $ref: '#/definitions/nullable-string' },
        booksShipmentId: { $ref: '#/definitions/nullable-string' },
        boOrderId: { $ref: '#/definitions/nullable-string' },
        isActive: { $ref: '#/definitions/boolean' },
        documentProductLinePrestationStatusId: { $ref: '#/definitions/integer' },
      },
    },
  },
  definitions: {
    integer: {
      type: 'integer',
      minimum: 1,
    },
    'non-empty-string': {
      type: 'string',
      minLength: 1,
    },
    'nullable-string': {
      type: ['string', 'null'],
    },
    date: {
      type: 'string',
      format: 'date',
    },
    boolean: {
      type: 'boolean',
    },
  },
};

export default transpileSchema(createDocumentProductLinePrestationSchema);
