import { transpileSchema } from 'middy-layer';

const updateDocumentProductLinePrestationStatusSchema = {
  type: 'object',
  required: ['body'],
  properties: {
    body: {
      type: 'object',
      required: ['name', 'key'],
      properties: {
        name: { $ref: '#/definitions/non-empty-string' },
        key: { $ref: '#/definitions/non-empty-string' },
      },
    },
  },
  definitions: {
    'non-empty-string': {
      type: 'string',
      minLength: 1,
    },
  },
};

export default transpileSchema(updateDocumentProductLinePrestationStatusSchema);
