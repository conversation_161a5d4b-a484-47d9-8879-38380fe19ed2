import { transpileSchema } from 'middy-layer';

const updateDocumentProductLinePrestationSubOptionSchema = {
  type: 'object',
  required: ['body'],
  properties: {
    body: {
      type: 'object',
      required: ['documentProductLinePrestationId', 'subOptionId'],
      properties: {
        documentProductLinePrestationId: { $ref: '#/definitions/integer' },
        subOptionId: { $ref: '#/definitions/integer' },
      },
    },
  },
  definitions: {
    integer: {
      type: 'integer',
      minimum: 1,
    },
    'nullable-string': {
      type: ['string', 'null'],
    },
  },
};

export default transpileSchema(updateDocumentProductLinePrestationSubOptionSchema);
