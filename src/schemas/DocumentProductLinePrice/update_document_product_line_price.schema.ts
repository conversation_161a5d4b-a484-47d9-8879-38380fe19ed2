import { transpileSchema } from 'middy-layer';

const updateDocumentProductLinePriceSchema = {
  type: 'object',
  required: ['body'],
  properties: {
    body: {
      type: 'object',
      required: ['documentProductLineId'],
      properties: {
        documentProductLineId: { $ref: '#/definitions/integer' },
      },
    },
  },
  definitions: {
    integer: {
      type: 'integer',
      minimum: 1,
    },
    'nullable-string': {
      type: ['string', 'null'],
    },
  },
};

export default transpileSchema(updateDocumentProductLinePriceSchema);
