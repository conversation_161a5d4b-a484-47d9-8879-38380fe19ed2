import { transpileSchema } from 'middy-layer';

const createDocumentProductLineSubOptionSchema = {
  type: 'object',
  required: ['body'],
  properties: {
    body: {
      type: 'object',
      required: ['documentProductLineId', 'subOptionId'],
      properties: {
        documentProductLineId: { $ref: '#/definitions/integer' },
        subOptionId: { $ref: '#/definitions/integer' },
      },
    },
  },
  definitions: {
    integer: {
      type: 'integer',
      minimum: 1,
    },
    'nullable-string': {
      type: ['string', 'null'],
    },
  },
};

export default transpileSchema(createDocumentProductLineSubOptionSchema);
