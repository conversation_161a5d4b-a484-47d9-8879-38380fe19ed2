'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface) {
    await queryInterface.bulkInsert(
      'document_statuses',
      [
        {
          id: 1,
          name: '<PERSON><PERSON><PERSON><PERSON>',
          key: 'draft',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 2,
          name: '<PERSON>voy<PERSON>',
          key: 'sent',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 3,
          name: 'Accept<PERSON>',
          key: 'accepted',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 4,
          name: 'Confirmée',
          key: 'confirmed',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ],
      {},
    );
  },

  async down(queryInterface) {
    await queryInterface.bulkDelete('document_statuses', null, {});
  },
};
