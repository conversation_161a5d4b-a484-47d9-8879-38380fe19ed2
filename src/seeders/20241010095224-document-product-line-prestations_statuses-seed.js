'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface) {
    await queryInterface.bulkInsert(
      'document_product_line_prestation_statuses',
      [
        {
          id: 1,
          name: '<PERSON> Traiter',
          key: 'A_TRAITER',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 2,
          name: '<PERSON> <PERSON>',
          key: 'SUR_CHANTIER',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 3,
          name: 'Planifi<PERSON>',
          key: 'PLANIFIEE',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 4,
          name: '<PERSON><PERSON><PERSON>',
          key: 'CANC<PERSON>',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 5,
          name: 'En Déchèterie',
          key: 'EN_DECHETERIE',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ],
      {},
    );
  },

  async down (queryInterface) {
    await queryInterface.bulkDelete('document_product_line_prestation_statuses', null, {});
  }
};
