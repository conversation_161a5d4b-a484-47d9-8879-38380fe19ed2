'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    // Step 1: Drop Foreign Key Constraint
    await queryInterface.sequelize.query(
      `
        ALTER TABLE documents
        DROP FOREIGN KEY documents_ibfk_2;
        `,
      { transaction }
    );

    // Update all existing data
    await queryInterface.bulkUpdate(
      'document_statuses',
      { status: 'draft', zohoStatus: 'draft', zohoCurrentSubStatus: 'draft' },
      {name: 'Brouillon'},
      { transaction }
    );
    await queryInterface.bulkUpdate(
      'document_statuses',
      { status: 'sent', zohoStatus: 'sent', zohoCurrentSubStatus: 'sent' },
      {name: 'Envoyé'},
      { transaction }
    );
    await queryInterface.bulkUpdate(
      'document_statuses',
      { status: 'accepted', zohoStatus: 'accepted', zohoCurrentSubStatus: 'accepted' },
      {name: 'Accepté'},
      { transaction }
    );
    await queryInterface.bulkUpdate(
      'document_statuses',
      { status: 'open', zohoStatus: 'open', zohoCurrentSubStatus: 'open' },
      {name: 'Confirmée'},
      { transaction }
    );

    // Insert new data rows
    await queryInterface.bulkInsert(
      'document_statuses',
      [
        {
          name: 'Facturé',
          key: 'accepted',
          status: 'invoiced',
          zohoStatus: 'invoiced',
          zohoCurrentSubStatus: 'invoiced',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          name: 'Refusé',
          key: 'accepted',
          status: 'declined',
          zohoStatus: 'declined',
          zohoCurrentSubStatus: 'declined',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          name: 'Expiré',
          key: 'accepted',
          status: 'expired',
          zohoStatus: 'expired',
          zohoCurrentSubStatus: 'expired',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          name: 'Prise en compte',
          key: 'confirmed',
          status: 'considered',
          zohoStatus: 'open',
          zohoCurrentSubStatus: 'cs_priseen',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          name: 'A traiter',
          key: 'confirmed',
          status: 'toProcess',
          zohoStatus: 'open',
          zohoCurrentSubStatus: 'cs_atraite',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          name: 'Traitée',
          key: 'confirmed',
          status: 'processed',
          zohoStatus: 'open',
          zohoCurrentSubStatus: 'cs_traite',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          name: 'Modification en cours',
          key: 'confirmed',
          status: 'modificationInProgress',
          zohoStatus: 'open',
          zohoCurrentSubStatus: 'cs_modific',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          name: 'Annulée',
          key: 'confirmed',
          status: 'void',
          zohoStatus: 'void',
          zohoCurrentSubStatus: 'void',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          name: 'Facturée / Fermée',
          key: 'confirmed',
          status: 'invoicedOrder',
          zohoStatus: 'invoiced',
          zohoCurrentSubStatus: 'closed',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          name: 'Partiellement facturée',
          key: 'confirmed',
          status: 'partiallyInvoiced',
          zohoStatus: 'partially_invoiced',
          zohoCurrentSubStatus: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ],
      { transaction }
    );

    await queryInterface.sequelize.query(
      `
        ALTER TABLE documents
        ADD CONSTRAINT documents_ibfk_2
        FOREIGN KEY (documentStatusId)
        REFERENCES document_statuses(id)
        ON DELETE RESTRICT
        ON UPDATE CASCADE;
        `,
      { transaction }
    );
  },

  async down () {}
};
