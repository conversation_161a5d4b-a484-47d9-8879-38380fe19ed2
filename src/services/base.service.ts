import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { pick } from 'utils-layer';
import { RequestMiddleware } from 'middy-layer';
import { CreationAttributes, ErrorResponse, Model, SuccessResponse, Repository } from 'database-layer';

export interface IService {
  getAll(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult>;
  paginate(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult>;
  getById(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult>;
  create(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult>;
  update(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult>;
  delete(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult>;
  activate(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult>;
  deactivate(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult>;
}

export abstract class BaseService<TModel extends Model, TInput extends CreationAttributes<TModel>> implements IService {
  protected readonly repository: Repository<TModel>;
  protected attributes: string[];
  protected getCreateSchema?(): Partial<Record<keyof TInput, unknown>>;
  protected getUpdateSchema?(): Partial<Record<keyof TInput, unknown>>;

  /**
   * Constructor for BaseService.
   * @param {Repository<TModel>} repository - An instance of a type that extends {@link Repository}.
   * @param {string[]} attributes - A list of strings of the attributes of the model.
   */
  constructor(repository: Repository<TModel>, attributes: string[]) {
    this.repository = repository;
    this.attributes = attributes;
  }

  /**
   * Handles a request by calling the provided handler with the repository and event.
   * @param {function(Repository<TModel>, APIGatewayProxyEvent): Promise<any>} handler - The handler to call.
   * @param {APIGatewayProxyEvent} event - The event to pass to the handler.
   * @return {Promise<APIGatewayProxyResult>} A promise that resolves to an APIGatewayProxyResult object.
   * The result object contains either a successful response or an error response.
   * If the handler throws an error, the error is logged to the console and an ErrorResponse is returned.
   * If the handler returns a value, a SuccessResponse is returned with the value.
   */
  protected async handleRequest(
    handler: (repository: Repository<TModel>, event: APIGatewayProxyEvent) => Promise<unknown>,
    event: APIGatewayProxyEvent,
  ): Promise<APIGatewayProxyResult> {
    try {
      const result = await handler(this.repository, event);
      return new SuccessResponse(result);
    } catch (error) {
      console.log(error);
      return new ErrorResponse((error as Error).message, error);
    }
  }

  /**
   * Retrieves a list of entities from the database based on the provided query string parameters.
   * @param {APIGatewayProxyEvent} event - The event object that contains the query string parameters.
   * @returns {Promise<APIGatewayProxyResult>} A promise that resolves to an APIGatewayProxyResult object containing the list of entities.
   */
  public getAll = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    return this.handleRequest(async (repository, event) => {
      const { queryStringParameters } = event;
      return repository.findByCriteria(queryStringParameters);
    }, event);
  };

  /**
   * Retrieves a paginated list of entities from the database based on the provided query string parameters.
   * @param {APIGatewayProxyEvent} event - The event object that contains the query string parameters.
   * @returns {Promise<APIGatewayProxyResult>} A promise that resolves to an APIGatewayProxyResult object containing the list of entities.
   */
  public paginate = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    return this.handleRequest(async (repository, event) => {
      const { queryStringParameters } = event;
      return repository.paginateByCriteria(queryStringParameters);
    }, event);
  };

  /**
   * Retrieves an entity from the database by its ID.
   * @param {APIGatewayProxyEvent} event - The event object that contains the path parameters and query string parameters.
   * @returns {Promise<APIGatewayProxyResult>} A promise that resolves to an APIGatewayProxyResult object containing the entity.
   */
  public getById = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    return this.handleRequest(async (repository, event) => {
      const { pathParameters, queryStringParameters } = event;
      const id = parseInt(pathParameters?.id as string);
      return repository.findByIdOrFail(id, queryStringParameters);
    }, event);
  };

  /**
   * Creates a new entity in the database using the provided request body.
   * @param {APIGatewayProxyEvent} event - The event object that contains the request body.
   * @returns {Promise<APIGatewayProxyResult>} A promise that resolves to an APIGatewayProxyResult object containing the created entity.
   */
  public create = RequestMiddleware.handle(
    async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
      return this.handleRequest(async (repository, event) => {
        const { body } = event;
        const input = pick(this.attributes, body as unknown as TInput) as TInput;
        return repository.create(input);
      }, event);
    },
    this.getCreateSchema ? this.getCreateSchema() : null,
  );

  /**
   * Updates an entity in the database using the provided request body.
   * @param {APIGatewayProxyEvent} event - The event object that contains the request body and path parameters.
   * @returns {Promise<APIGatewayProxyResult>} A promise that resolves to an APIGatewayProxyResult object containing the updated entity.
   */
  public update = RequestMiddleware.handle(
    async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
      return this.handleRequest(async (repository, event) => {
        const { pathParameters, body } = event;
        const id = parseInt(pathParameters?.id as string);
        const input = pick(this.attributes, body as unknown as TInput) as TInput;
        const existingEntity = await repository.findOneOrFail({ where: { id } });
        return repository.update(existingEntity, input);
      }, event);
    },
    this.getUpdateSchema ? this.getUpdateSchema() : null,
  );

  /**
   * Deletes an entity in the database.
   * @param {APIGatewayProxyEvent} event - The event object that contains the path parameters.
   * @returns {Promise<APIGatewayProxyResult>} A promise that resolves to an APIGatewayProxyResult object containing a success message.
   */
  public delete = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    return this.handleRequest(async (repository, event) => {
      const { pathParameters } = event;
      const id = parseInt(pathParameters?.id as string);
      const result = await repository.deleteById(id);
      return { isSuccess: result, message: 'Entity deleted successfully' };
    }, event);
  };

  /**
   * Activates an entity in the database.
   * @param {APIGatewayProxyEvent} event - The event object that contains the path parameters.
   * @returns {Promise<APIGatewayProxyResult>} A promise that resolves to an APIGatewayProxyResult object containing a success message.
   */
  public activate = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    return this.handleRequest(async (repository, event) => {
      const { pathParameters } = event;
      const id = parseInt(pathParameters?.id as string);

      return await repository.active(id);
    }, event);
  };

  /**
   * Deactivates an entity in the database.
   * @param {APIGatewayProxyEvent} event - The event object that contains the path parameters.
   * @returns {Promise<APIGatewayProxyResult>} A promise that resolves to an APIGatewayProxyResult object containing a success message.
   */
  public deactivate = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    return this.handleRequest(async (repository, event) => {
      const { pathParameters } = event;
      const id = parseInt(pathParameters?.id as string);

      return await repository.deactive(id);
    }, event);
  };
}
