import { BaseService } from './base.service';
import DocumentCCContactRepository from '../repository/document-cc-contact.repository';
import { DocumentCCContact, DocumentCCContactInput, DocumentCCContactAttributes } from '../models';
import { createDocumentCCContactSchema, updateDocumentCCContactSchema } from '../schemas/DocumentCCContact';

export class DocumentCCContactService extends BaseService<DocumentCCContact, DocumentCCContactInput> {
  constructor() {
    super(new DocumentCCContactRepository(), DocumentCCContactAttributes);
  }

  protected getCreateSchema(): Partial<Record<keyof DocumentCCContact, unknown>> {
    return createDocumentCCContactSchema;
  }

  protected getUpdateSchema(): Partial<Record<keyof DocumentCCContact, unknown>> {
    return updateDocumentCCContactSchema;
  }
}

const service = new DocumentCCContactService();
export default service;
