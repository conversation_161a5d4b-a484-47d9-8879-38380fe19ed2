import { BaseService } from './base.service';
import DocumentCCLibresContactRepository from '../repository/document-cc-libres-contact.repository';
import { DocumentCCLibresContact, DocumentCCLibresContactInput, DocumentCCLibresContactAttributes } from '../models';
import {
  createDocumentCCLibresContactSchema,
  updateDocumentCCLibresContactSchema,
} from '../schemas/DocumentCCLibresContact';

export class DocumentCCLibresContactService extends BaseService<DocumentCCLibresContact, DocumentCCLibresContactInput> {
  constructor() {
    super(new DocumentCCLibresContactRepository(), DocumentCCLibresContactAttributes);
  }

  protected getCreateSchema(): Partial<Record<keyof DocumentCCLibresContact, unknown>> {
    return createDocumentCCLibresContactSchema;
  }

  protected getUpdateSchema(): Partial<Record<keyof DocumentCCLibresContact, unknown>> {
    return updateDocumentCCLibresContactSchema;
  }
}

const service = new DocumentCCLibresContactService();
export default service;
