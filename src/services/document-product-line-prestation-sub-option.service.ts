import { BaseService } from './base.service';
import {
  DocumentProductLinePrestationSubOption,
  DocumentProductLinePrestationSubOptionAttributes,
  DocumentProductLinePrestationSubOptionInput,
  DocumentProductLineSubOption,
} from '../models';
import {
  createDocumentProductLinePrestationSubOptionSchema,
  updateDocumentProductLinePrestationSubOptionSchema,
} from '../schemas/DocumentProductLinePrestationSubOption';
import DocumentProductLinePrestationSubOptionRepository from '../repository/document-product-line-prestation-sub-option.repository';
export class DocumentProductLinePrestationSubOptionService extends BaseService<
  DocumentProductLinePrestationSubOption,
  DocumentProductLinePrestationSubOptionInput
> {
  constructor() {
    super(new DocumentProductLinePrestationSubOptionRepository(), DocumentProductLinePrestationSubOptionAttributes);
  }

  protected getCreateSchema(): Partial<Record<keyof DocumentProductLineSubOption, unknown>> {
    return createDocumentProductLinePrestationSubOptionSchema;
  }

  protected getUpdateSchema(): Partial<Record<keyof DocumentProductLineSubOption, unknown>> {
    return updateDocumentProductLinePrestationSubOptionSchema;
  }
}

const service = new DocumentProductLinePrestationSubOptionService();
export default service;
