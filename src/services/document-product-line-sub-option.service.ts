import DocumentProductLineSubOptionRepositoryy from '../repository/document-product-line-sub-option.repository';
import { BaseService } from './base.service';
import {
  DocumentProductLineSubOption,
  DocumentProductLineSubOptionAttributes,
  DocumentProductLineSubOptionInput,
} from '../models';
import {
  createDocumentProductLineSubOptionSchema,
  updateDocumentProductLineSubOptionSchema,
} from '../schemas/DocumentProductLineSubOption';
export class DocumentProductLineSubOptionService extends BaseService<
  DocumentProductLineSubOption,
  DocumentProductLineSubOptionInput
> {
  constructor() {
    super(new DocumentProductLineSubOptionRepositoryy(), DocumentProductLineSubOptionAttributes);
  }

  protected getCreateSchema(): Partial<Record<keyof DocumentProductLineSubOption, unknown>> {
    return createDocumentProductLineSubOptionSchema;
  }

  protected getUpdateSchema(): Partial<Record<keyof DocumentProductLineSubOption, unknown>> {
    return updateDocumentProductLineSubOptionSchema;
  }
}

const service = new DocumentProductLineSubOptionService();
export default service;
