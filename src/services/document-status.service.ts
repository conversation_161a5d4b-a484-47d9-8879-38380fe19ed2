import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import DocumentStatusRepository from '../repository/document-status.repository';
import { ErrorResponse, SuccessResponse } from 'database-layer';

/**
 * Retrieves a list of documentStatuses based on the provided event.
 *
 * The function retrieves a list of documentStatuses based on the provided query string parameters.
 * The query string parameters are used to filter the documentStatuses. The function returns a promise that resolves to an APIGatewayProxyResult object containing the list of documentStatuses.
 *
 * @return {Promise<APIGatewayProxyResult>} A promise that resolves to an APIGatewayProxyResult object containing the list of documentStatuses.
 */
const getDocumentStatuses = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const { queryStringParameters } = event;
    const documentStatusRepository = new DocumentStatusRepository();
    // Paginate the documentTypes based on the provided query string parameters.
    const documentStatuses = await documentStatusRepository.findByCriteria(queryStringParameters);
    // Return a successful response with the list of documentStatuses.
    return new SuccessResponse(documentStatuses);
  } catch (error) {
    // Return an error response with the error message and the error object itself.
    return new ErrorResponse((error as Error).message, error);
  }
};

/**
 * Retrieves a documentStatus by its ID.
 *
 * @param {APIGatewayProxyEvent} event - The event object containing the request information.
 * @return {Promise<APIGatewayProxyResult>} A promise that resolves to the API Gateway proxy result.
 */
const getDocumentStatusById = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const { queryStringParameters, pathParameters } = event;
    const id = parseInt(pathParameters?.id as string);
    const documentStatusRepository = new DocumentStatusRepository();
    // Try to find the documentStatus by its ID.
    const documentStatus = await documentStatusRepository.findByIdOrFail(id, queryStringParameters);
    // Return a successful response with the documentStatus.
    return new SuccessResponse(documentStatus);
  } catch (error) {
    // Return an error response with the error message and the error object itself.
    return new ErrorResponse((error as Error).message, error);
  }
};

export { getDocumentStatuses, getDocumentStatusById };
