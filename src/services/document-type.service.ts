import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { ErrorResponse, SuccessResponse } from 'database-layer';
import DocumentTypeRepository from '../repository/document-type.repository';

/**
 * Service to get a list of document types. The list is paginated based on the provided query string parameters.
 * @param event API Gateway Proxy Event
 * @returns API Gateway Proxy Result
 */
const getDocumentTypes = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const { queryStringParameters } = event;
    // Create a new DocumentTypeRepository instance
    const documentTypeRepository = new DocumentTypeRepository();
    // Paginate the documentTypes based on the provided query string parameters.
    const documentTypes = await documentTypeRepository.paginateByCriteria(queryStringParameters);
    // Return a successful response with the list of documentTypes.
    return new SuccessResponse(documentTypes);
  } catch (error) {
    // Return an error response with the error message and the error object itself.
    return new ErrorResponse((error as Error).message, error);
  }
};

export { getDocumentTypes };
