import DocumentRepository from '../repository/document.repository';
import {
  DocumentAttributes,
  Document,
  DocumentInput,
  DocumentCCLibresContactAttributes,
  DocumentCCLibresContactInput,
  DocumentProductLineInput,
  DocumentProductLinePrestation,
  DocumentProductLinePrestationSubOption,
  DocumentProductLinePrice,
  DocumentProductLineSubOption,
  Demander,
  DocumentFileUploadInput,
  DocumentType,
} from '../models';
import { BaseService } from './base.service';
import { createDocumentSchema, updateDocumentSchema } from '../schemas/Document';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { CriteriaOptions, ErrorResponse, ErrorWithName, Op, sequelize, SuccessResponse } from 'database-layer';
import { RequestMiddleware } from 'middy-layer';
import { pick } from 'utils-layer';
import { publishZohoEvent } from './sns.service';
import DocumentCCLibresContactRepository from '../repository/document-cc-libres-contact.repository';
import DocumentCCContactRepository from '../repository/document-cc-contact.repository';
import DocumentProductLineRepository from '../repository/document_product_lines.repository';
import {
  FileUploadResponse,
  ZohoBodyResponseEvent,
  ZohoEstimate,
  ZohoEstimateAndOrderResponseEvent,
  ZohoSalesOrder,
} from '../types';
import DocumentFileUploadRepository from '../repository/document-file-upload.repository';
import DocumentStatusRepository from '../repository/document-status.repository';
import DocumentTypeRepository from '../repository/document-type.repository';
import { invokeLambda } from './lambda.service';
import { DOCUMENT_STATUS, DOCUMENT_TYPES, formatCoordinate } from '../utils';
import axios from 'axios';
import {
  NODE_ENV,
  SYNC_CONTACT_ADDRESS_FROM_ZOHO,
  SYNC_DOCUMENT_TO_ZOHO,
  COUNTRY_REGIONS,
  GOOGLE_MAPS_API_KEY,
} from '../constant';

export type DocumentDataInput = DocumentInput & {
  documentId?: string;
  isCreateEstimateAndOrder?: boolean;
  contactPersons: string[];
  crmContactId: string;
  customerId: string;
  salespersonId?: string;
  salespersonPhone?: string;
  booksContactId?: string;
  lastModifyById?: string;
  lastModifyName?: string;
  lastModifyPhone?: string;
  lastModifyEmail?: string;
  objectDuDocument?: string;
  lineItems: (DocumentProductLineInput & {
    prestationDateLine?: DocumentProductLinePrestation | DocumentProductLinePrestationSubOption[];
    productLinePrices?: DocumentProductLinePrice[];
    productLineSubOptions?: DocumentProductLineSubOption[];
    priceFamilyValue?: string | number;
    name?: string;
    booksProductId?: string;
  })[];
  ccContactPersons?: number[];
  submitType?: string;
  estimateId?: string;
  documentStatus?: string;
  paiement?: string;
  montantPaiement?: string | number;
  conditionsDePaiement?: string | number;
  paymentProof?: DocumentFileUploadInput & {
    status_file?: string;
  };
  valueDemander?: Demander;
  estimateNumber?: string;
  contact?: Record<string, unknown>;
  contactPerson?: Record<string, unknown>;
  vendeurCrm?: string;
};
export default class DocumentService extends BaseService<Document, DocumentInput> {
  private readonly productLineItemRepository: DocumentProductLineRepository;
  private readonly documentRepository: DocumentRepository;
  constructor() {
    super(new DocumentRepository(), DocumentAttributes);
    this.productLineItemRepository = new DocumentProductLineRepository();
    this.documentRepository = new DocumentRepository();
  }

  public paginate = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
      console.log('DocumentService paginate >>>>>>>>>>>>>>>>>');
      const queryStringParams = event.queryStringParameters || {};
      const { limit, page, orderBy, fullSearch, ...otherFilters } = queryStringParams;
      console.log('fullSearch', fullSearch);
      const data = await this.documentRepository.getDocumentsFilterQuery(
        limit,
        page,
        orderBy,
        fullSearch,
        otherFilters,
      );
      // Return a successful response with the list of documents.
      return new SuccessResponse(data);
    } catch (error) {
      console.log(error);
      // Return an error response with the error message and the error object itself.
      return new ErrorResponse((error as Error).message, error);
    }
  };

  protected getCreateSchema(): Partial<Record<keyof Document, unknown>> {
    return createDocumentSchema;
  }

  protected getUpdateSchema(): Partial<Record<keyof Document, unknown>> {
    return updateDocumentSchema;
  }

  /**
   * Get the document detail
   * @param event - The API Gateway proxy event containing the request data
   * @returns A promise that resolves to an API Gateway proxy result with the document detail or an error response in case of failure
   */
  public getDocumentDetail = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
      const { pathParameters, queryStringParameters } = event;
      const id = pathParameters?.id;
      const options: CriteriaOptions<Document> = {};
      if (id) {
        options.where = { [Op.or]: [{ id: id }, { booksDocumentId: id }] };
      }
      const document = await new DocumentRepository().findByCriteria(queryStringParameters, options);
      return new SuccessResponse(document);
    } catch (error) {
      // Return an error response with the error message and the error object itself.
      return new ErrorResponse((error as Error).message, error);
    }
  };

  /**
   * Get provider invoice data for a service provider
   * @param event - The API Gateway proxy event or Step Function input containing the request data
   * @returns A promise that resolves to an API Gateway proxy result with the provider invoice data or an error response in case of failure
   */
  public getProviderInvoiceData = async (event: Record<string, unknown>): Promise<APIGatewayProxyResult> => {
    try {
      console.log('🔄 GetProviderInvoiceData: Received event:', JSON.stringify(event, null, 2));

      // Handle different input formats
      let serviceProviderId: string | undefined;

      // Case 1: Direct Step Function input with serviceProviderId
      if (event.serviceProviderId) {
        serviceProviderId = String(event.serviceProviderId);
      }
      // Case 2: Step Function input with body.id (from documentId)
      else if (event.body && typeof event.body === 'object' && event.body !== null && 'id' in event.body) {
        serviceProviderId = String((event.body as Record<string, unknown>).id);
      }
      // Case 3: API Gateway event with path parameters
      else if (event.pathParameters && typeof event.pathParameters === 'object' && event.pathParameters !== null && 'serviceProviderId' in event.pathParameters) {
        serviceProviderId = String((event.pathParameters as Record<string, unknown>).serviceProviderId);
      }
      // Case 4: API Gateway event with query parameters
      else if (event.queryStringParameters && typeof event.queryStringParameters === 'object' && event.queryStringParameters !== null && 'serviceProviderId' in event.queryStringParameters) {
        serviceProviderId = String((event.queryStringParameters as Record<string, unknown>).serviceProviderId);
      }
      // Case 5: Direct documentId parameter (for backward compatibility)
      else if (event.documentId) {
        serviceProviderId = String(event.documentId);
      }

      if (!serviceProviderId) {
        console.error('❌ No serviceProviderId found in event');
        return new ErrorResponse('Service Provider ID is required. Provide it as serviceProviderId, documentId, or in pathParameters/queryStringParameters', null);
      }

      console.log(`🔄 GetProviderInvoiceData: Fetching data for serviceProviderId: ${serviceProviderId}`);

      const documentRepository = new DocumentRepository();

      // For Step Functions, limit data by default to avoid payload size issues
      // First, get a small sample to check data size
      const sampleData = await documentRepository.getProviderInvoiceData(parseInt(serviceProviderId), 5);

      if (sampleData.length === 0) {
        console.log(`✅ GetProviderInvoiceData: No records found for serviceProviderId: ${serviceProviderId}`);
        return new SuccessResponse({
          serviceProviderId: parseInt(serviceProviderId),
          data: [],
          count: 0
        });
      }

      // Calculate approximate size per record
      const sampleSize = JSON.stringify(sampleData).length;
      const avgRecordSize = sampleSize / sampleData.length;
      const maxRecords = Math.floor(200000 / avgRecordSize); // Stay under 200KB

      console.log(`📊 Average record size: ${avgRecordSize} bytes, limiting to ${maxRecords} records`);

      // Get the limited dataset
      const providerInvoiceData = await documentRepository.getProviderInvoiceData(
        parseInt(serviceProviderId),
        Math.min(maxRecords, 50) // Cap at 50 records max
      );

      console.log(`✅ GetProviderInvoiceData: Returning ${providerInvoiceData.length} provider invoice records`);

      return new SuccessResponse({
        serviceProviderId: parseInt(serviceProviderId),
        data: providerInvoiceData,
        count: providerInvoiceData.length,
        limitedForStepFunctions: true,
        maxRecordsReturned: Math.min(maxRecords, 50)
      });
    } catch (error) {
      console.error('❌ Error in getProviderInvoiceData:', error);
      return new ErrorResponse((error as Error).message, error);
    }
  };

  /**
   * Handles the Zoho update document request by updating the document
   * in the repository using the provided event data.
   *
   * @param event - The API Gateway proxy event containing the request data.
   * @returns A promise that resolves to an API Gateway proxy result with the
   * updated document data or an error response in case of failure.
   * @throws Error - Throws an error if the `booksDocumentId` is not provided
   * in the request body.
   */
  public zohoUpdateDocument = RequestMiddleware.handle(
    async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
      try {
        const body = event.body as unknown as DocumentDataInput;
        const { booksDocumentId } = body;
        if (!booksDocumentId) throw new Error('booksDocumentId is required');

        const documentRepository = new DocumentRepository();
        const document = await documentRepository.findOneOrFail({
          where: { booksDocumentId },
        });

        const input = pick(DocumentAttributes, body) as DocumentDataInput;
        const updateObject = await documentRepository.update(document, input);
        return new SuccessResponse(updateObject);
      } catch (error) {
        console.error(error);
        // Return an ErrorResponse with the error message and the error object.
        return new ErrorResponse((error as Error).message, error);
      }
    },
  );

  /**
   * Updates the status of a document in the repository based on the provided event data.
   * @param event - The API Gateway proxy event containing the request data.
   * @returns A promise that resolves to an API Gateway proxy result with the updated document data or an error response in case of failure.
   */
  public updateStatusDocument = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
      console.log('updateStatusDocument start');
      const { pathParameters } = event;
      const { document_status } = JSON.parse(event.body) as { document_status: string };
      const id = parseInt(pathParameters?.id as string);
      const documentRepository = new DocumentRepository();
      // Fetch the document
      const document = await documentRepository.findOneOrFail({ where: { id }, include: ['DocumentStatus'] });
      if (!document) {
        return new ErrorResponse('Document not found', null, 404);
      }
      // Handle 'Prise en compte' status
      if (document_status == DOCUMENT_STATUS.CONSIDERED) {
        // Status document current is A traiter
        const documentStatusPriseEnCompte = await new DocumentStatusRepository().findOneOrFail({
          where: { status: DOCUMENT_STATUS.CONSIDERED },
        });
        await documentRepository.update(document, {
          documentStatusId: documentStatusPriseEnCompte.id,
        });
        const updateObject = await documentRepository.findOneOrFail({
          where: { id },
          include: ['DocumentStatus', 'Vendeur', 'Referent'],
        });
        console.log('updateObject.DocumentStatus', updateObject.DocumentStatus);
        await publishZohoEvent('UPDATE-STATUS-ORDER', updateObject);
        return new SuccessResponse(updateObject);
      }
      // Handle 'Traité' status
      if (document_status == DOCUMENT_STATUS.PROCESSED) {
        // Status document status Traité
        const documentStatusTraite = await new DocumentStatusRepository().findOneOrFail({
          where: { status: DOCUMENT_STATUS.PROCESSED },
        });
        // Update document
        await documentRepository.update(document, {
          documentStatusId: documentStatusTraite.id,
        });
        const updateObject = await documentRepository.findOneOrFail({
          where: { id },
          include: ['DocumentStatus', 'Vendeur', 'Referent'],
        });
        console.log('updateObject.DocumentStatus', updateObject.DocumentStatus);
        await publishZohoEvent('UPDATE-STATUS-ORDER', updateObject);
        return new SuccessResponse(updateObject);
      }
    } catch (error) {
      console.error(error);
      return new ErrorResponse((error as Error).message, error);
    }
  };

  /**
   * Create or update a document using Step Functions
   * @param event - The event data containing the document details
   * @returns A promise that resolves to an array containing the updated document and the extra data
   */
  public createOrUpdateDocument = async (event: DocumentDataInput) => {
    console.log('event', event);
    console.log('createOrUpdateDocument', event);
    try {
      return await sequelize.transaction(async () => {
        let action = 'CREATE';
        let savedDocument: Document;
        let order: Document;
        let extraData: { [key: string]: unknown };
        // If the document ID is provided, update the document
        if (event.documentId) {
          [savedDocument, extraData] = await this.updateDocument(event);
          action = 'UPDATE';
        } else if (event.isCreateEstimateAndOrder) {
          // If the isCreateEstimateAndOrder is true, create a new estimate AND order
          [savedDocument, extraData, order] = await this.createEstimateAndOrder(event);
          action = 'CREATE-ESTIMATE-AND-ORDER';
        } else {
          // If the isCreateEstimateAndOrder is not provided, create a new estimate or order based on the submitType
          [savedDocument, extraData] = await this._createEstimateOrOrder(event);
        }
        // Send the updated document to Zoho Service and sync it to Zoho
        // This action will call the Zoho Service to sync the document to Zoho
        console.log('savedDocument', savedDocument);
        console.log('extraData', extraData);
        console.log('synce zoho function', SYNC_DOCUMENT_TO_ZOHO);
        // The invokeLambda function is not working in the local unit test, so we need to skip the sync to Zoho
        let zohoResponse: ZohoBodyResponseEvent | ZohoEstimateAndOrderResponseEvent;
        if (process.env.NODE_ENV !== NODE_ENV.TEST) {
          // catch the error and throw it with the name ZohoSyncError to handle the alert message on Frontend
          try {
            zohoResponse = await invokeLambda<ZohoBodyResponseEvent | ZohoEstimateAndOrderResponseEvent>({
              FunctionName: SYNC_DOCUMENT_TO_ZOHO,
              Payload: JSON.stringify({
                action,
                data: { ...savedDocument.toJSON(), ...extraData },
              }),
            });
            console.log('zohoResponse', zohoResponse);
          } catch (error) {
            console.error(error);
            throw ErrorWithName('ZohoSyncError', error, error.message);
          }
          // Update Zoho CRM ID in Document Service and Contact Address in Contact Service
          if (action === 'CREATE-ESTIMATE-AND-ORDER') {
            await Promise.all([
              this.syncDocumentAndContactFromZoho((zohoResponse as ZohoEstimateAndOrderResponseEvent).estimate),
              this.syncDocumentAndContactFromZoho((zohoResponse as ZohoEstimateAndOrderResponseEvent).order, 'order'),
            ]);
          } else {
            await this.syncDocumentAndContactFromZoho(
              zohoResponse as ZohoBodyResponseEvent,
              (zohoResponse as ZohoBodyResponseEvent).requestData?.data?.submitType === DOCUMENT_TYPES.ORDER
                ? 'order'
                : 'estimate',
            );
          }
        }
        return {
          ...(order ?? savedDocument).toJSON(),
          success: !(zohoResponse?.success === false),
          error: zohoResponse?.error,
        };
      });
    } catch (error) {
      console.error(error);
      // Return an ErrorResponse with the error message and the error object.
      throw error;
    }
  };

  /**
   * Create a new estimate OR order
   * @param data - The data to create the estimate or order
   * @returns A promise that resolves to an array containing the created document and the extra data
   */
  public _createEstimateOrOrder = async (data: DocumentDataInput): Promise<[Document, { [key: string]: unknown }]> => {
    const {
      lineItems,
      ccContactPersons,
      submitType,
      estimateId,
      documentStatus,
      paymentProof,
      paiement,
      montantPaiement,
    } = data as unknown as DocumentDataInput;
    const input = pick(DocumentAttributes, data as unknown as DocumentInput) as DocumentInput;
    let getEstimateDocument = null;
    if (estimateId) {
      getEstimateDocument = await new DocumentRepository().findOne({
        where: { [Op.or]: [{ id: estimateId }, { booksDocumentId: estimateId }] },
        include: [{ model: DocumentType, required: true }],
      });
      if (submitType === 'order' && getEstimateDocument) {
        if (
          !getEstimateDocument?.referentDocumentID &&
          getEstimateDocument?.DocumentType?.key === DOCUMENT_TYPES.QUOTATION
        ) {
          input.referentDocumentID = getEstimateDocument.id;
        } else if (getEstimateDocument?.referentDocumentID) {
          getEstimateDocument = await new DocumentRepository().findOne({
            where: { id: getEstimateDocument?.referentDocumentID },
          });
        }
      }
    }
    // Create a new Document
    const document = await new DocumentRepository().create(input);
    const inputDocumentCCLibresContact = pick(
      DocumentCCLibresContactAttributes,
      data as unknown as DocumentCCLibresContactInput,
    ) as DocumentCCLibresContactInput;
    if (document) {
      // Create the document CC contacts
      const listCCContact = (ccContactPersons ?? [])?.map((item) => ({
        documentId: document.id,
        contactPersonId: item,
      }));
      await new DocumentCCContactRepository().bulkCreate(listCCContact);
      // Create the document CC libres contact
      if (inputDocumentCCLibresContact.email) {
        await new DocumentCCLibresContactRepository().create({
          documentId: document.id,
          email: inputDocumentCCLibresContact.email,
        });
      }

      if (lineItems?.length) {
        // Create product lines, prestations, sub-options and prices
        await new DocumentProductLineRepository().createDocumentProductLines(lineItems, document.id);
        // If the document is an order and there is a payment proof, create the payment proof
        if (submitType === 'order' && paymentProof) {
          await new DocumentFileUploadRepository().create({
            ...paymentProof,
            documentId: document.id,
          });
        }
      }
      // Save files
      const dataFiles = (data as unknown as { fileUpload: DocumentFileUploadInput })?.fileUpload ?? [];
      const documentFileUploadRepository = new DocumentFileUploadRepository();
      if (Array.isArray(dataFiles)) {
        // Create file
        await Promise.all(
          dataFiles.map((item) => {
            if (item.status_file === 'NEW') {
              return documentFileUploadRepository.create({
                name: item.file_name,
                documentId: document.id,
                url: item.url,
                type: 'BDC_CLIENT',
              });
            } else {
              return Promise.resolve(); // or return null, depending on your use case
            }
          }),
        );
      }
    }
    // Get the created document and the extra data after saving
    const [createdDocument, extraData] = await new DocumentRepository().getDocumentAfterSaving(
      document,
      data as unknown as DocumentDataInput,
    );
    // Add some more field to extra data for Zoho synchronization
    extraData.submitType = submitType;
    extraData.estimateId =
      submitType === 'order' && getEstimateDocument?.DocumentType?.key === DOCUMENT_TYPES.QUOTATION
        ? getEstimateDocument?.booksDocumentId
        : '';
    extraData.referenceNumber =
      submitType === 'order' && getEstimateDocument?.DocumentType?.key === DOCUMENT_TYPES.QUOTATION
        ? getEstimateDocument?.cdeZoho
        : '';
    extraData.documentStatus = documentStatus;
    extraData.montantPaiement = montantPaiement;
    extraData.paiement = paiement;
    extraData.lineItems = lineItems;

    return [createdDocument, extraData];
  };

  /**
   * Create a new estimate AND order
   * @param body - The data to create the estimate and order
   * @returns A promise that resolves to an array containing the created document and the extra data
   */
  public createEstimateAndOrder = async (
    body: DocumentDataInput,
  ): Promise<[Document, { [key: string]: unknown }, Document]> => {
    const input = body as unknown as DocumentDataInput;
    if (input.documentStatus === DOCUMENT_STATUS.ACCEPTED && input.submitType === DOCUMENT_TYPES.QUOTATION) {
      const [document, extraDocumentData] = await this._createEstimateOrOrder(body as unknown as DocumentDataInput);
      const documentStatus = await new DocumentStatusRepository().findOneByCriteria({
        status: DOCUMENT_STATUS.TO_PROCESS,
      });
      const documentType = await new DocumentTypeRepository().findOneByCriteria({
        key: DOCUMENT_TYPES.ORDER,
      });
      const dataSubmit = {
        ...input,
        estimateId: document?.id,
        documentStatusId: documentStatus?.id,
        submitType: DOCUMENT_TYPES.ORDER,
        documentTypeId: documentType?.id,
      };
      const [order] = await this._createEstimateOrOrder(dataSubmit as unknown as DocumentDataInput);
      const extraData = {
        ...extraDocumentData,
        orderId: order?.id,
        vendeurCrm: input.vendeurCrm,
      };
      // await publishZohoEvent('CREATE-ESTIMATE-AND-ORDER', document as Document, extraData);
      return [document, extraData, order];
    }
    throw ErrorWithName('Invalid input');
  };

  /**
   * Sync the estimate and contact from Zoho after the document is created/updated
   * @param zohoData - The Zoho data
   * @returns A promise that resolves to an array containing the updated estimate and the contact address
   */
  private async syncDocumentAndContactFromZoho(zohoData: ZohoBodyResponseEvent, documentType = 'estimate') {
    const [estimate, contactAddress] = await Promise.all([
      documentType === 'order' ? this.updateSalesOrderFromZoho(zohoData) : this.updateEstimateFromZoho(zohoData),
      invokeLambda({
        FunctionName: SYNC_CONTACT_ADDRESS_FROM_ZOHO,
        Payload: JSON.stringify(zohoData),
      }),
    ]);
    console.log('estimate', estimate);
    console.log('contactAddress', contactAddress);
    return { estimate, contactAddress };
  }

  public updateEstimateFromZoho = async (event: ZohoBodyResponseEvent): Promise<void> => {
    const { id } = event.requestData.data;
    const documentId = id;
    const zohoEstimate = event.responseData?.document as ZohoEstimate;
    const document = await this.repository.findByIdOrFail(documentId);
    console.log('document estimate', document);
    await document.update({
      booksDocumentId: zohoEstimate.estimate_id,
      cdeZoho: zohoEstimate.estimate_number,
    });
    await Promise.all(
      zohoEstimate.line_items?.map((lineItem) => {
        const productId = event.requestData.data.DocumentProductLines.find(
          (productLine) => productLine.booksProductId === lineItem.item_id,
        )?.productId;
        return this.productLineItemRepository.updateWhere(
          { documentId, ...(productId ? { productId } : {}), lineOrderNumber: lineItem.item_order },
          { booksProductLineId: lineItem.line_item_id, booksProductLineHeaderId: lineItem.header_id },
        );
      }),
    );
  };
  public updateSalesOrderFromZoho = async (event: ZohoBodyResponseEvent): Promise<void> => {
    const documentId = event.requestData.data.id;
    const zohoSalesOrder = event.responseData?.document as ZohoSalesOrder;
    console.log('zohoSalesOrder', JSON.stringify(zohoSalesOrder));
    const document = await this.repository.findById(documentId);
    console.log('document sales order', document);

    if (document) {
      const documentFileUpload = event.responseData?.documentFileUploads as {
        documents: FileUploadResponse[];
      };
      const fileUpload = documentFileUpload?.documents;
      const documentFileUploadsRepository = new DocumentFileUploadRepository();

      await document.update({
        booksDocumentId: zohoSalesOrder.salesorder_id,
        cdeZoho: zohoSalesOrder.salesorder_number,
      });

      await Promise.all([
        ...(zohoSalesOrder?.line_items?.map(async (lineItem) => {
          const productId = event.requestData.data?.DocumentProductLines?.find(
            (productLine) => productLine.booksProductId === lineItem.item_id,
          )?.productId;

          return this.productLineItemRepository.updateWhere(
            { documentId, ...(productId ? { productId } : {}), lineOrderNumber: lineItem.item_order },
            { booksProductLineId: lineItem.line_item_id, booksProductLineHeaderId: lineItem.header_id },
          );
        }) || []),

        ...(fileUpload?.map(async (fileUpload) => {
          return documentFileUploadsRepository.updateWhere(
            { documentId: documentId, name: fileUpload.file_name },
            { booksFileId: fileUpload.document_id },
          );
        }) || []),
      ]);
    }
  };

  /**
   * Update a document
   * @param body - The data to update the document
   * @returns A promise that resolves to an array containing the updated document and the extra data
   */
  public updateDocument = async (body: DocumentDataInput): Promise<[Document, { [key: string]: unknown }]> => {
    const { documentId } = body;
    const id = parseInt(documentId as string);
    const input = pick(DocumentAttributes, body as unknown as DocumentDataInput) as DocumentDataInput;
    const document = await this.repository.update(id, input);
    const { lineItems, ccContactPersons } = body as unknown as DocumentDataInput;
    if (ccContactPersons) {
      const existingDocumentCCContactIds = (await new DocumentCCContactRepository().pluck(
        {
          where: {
            documentId: document.id,
            contactPersonId: { [Op.in]: ccContactPersons },
          },
        },
        'contactPersonId',
      )) as number[];
      const createListCCContactData = (ccContactPersons ?? [])
        ?.filter((item) => !existingDocumentCCContactIds.includes(item))
        ?.map((item) => ({
          documentId: document.id,
          contactPersonId: item,
        }));
      const documentCCContactRepo = new DocumentCCContactRepository();
      await Promise.all([
        documentCCContactRepo.bulkCreate(createListCCContactData),
        documentCCContactRepo.delete({
          where: {
            documentId: document.id,
            contactPersonId: { [Op.notIn]: ccContactPersons },
          },
        }),
      ]);
    }
    const inputDocumentCCLibresContact = pick(
      DocumentCCLibresContactAttributes,
      body as unknown as DocumentCCLibresContactInput,
    ) as DocumentCCLibresContactInput;
    const documentCCLibresContactRepo = new DocumentCCLibresContactRepository();
    await documentCCLibresContactRepo.updateOrCreate(
      { documentId: document.id },
      { email: inputDocumentCCLibresContact.email },
    );
    if (lineItems) {
      await new DocumentProductLineRepository().updateDocumentProductLines(lineItems, document.id);
    }
    // Get the updated document and the extra data after saving
    return await new DocumentRepository().getDocumentAfterSaving(document, body as unknown as DocumentDataInput, true);

    // await publishZohoEvent('UPDATE', updatedDocument, extraData);
  };

  /**
   * Update contactID in document
   * @param event
   */
  public updateContactDocument = RequestMiddleware.handle(
    async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
      try {
        // Get the path parameters from the event.
        const body = event.body as unknown as Record<string, unknown>;
        const documentRepository = new DocumentRepository();
        if (body.clientType == 'individual') {
          await documentRepository.updateAll(
            { contactPersonId: body.ContactPersonId as number },
            { where: { contactId: body.contactOriginId as number } },
          );
        }
        await documentRepository.updateAll(
          { contactId: body.contactDestinationId as number },
          { where: { contactId: body.contactOriginId as number } },
        );
        // Return a SuccessResponse with the message.
        return new SuccessResponse({ 'Message:': ' Update success' });
      } catch (error) {
        console.error(error);
        // Return an ErrorResponse with the error message and the error object.
        return new ErrorResponse((error as Error).message, error);
      }
    },
  );

  /**
   * updateDocumentComment
   * @param event
   * @returns
   */
  public updateDocumentComment = async (event: APIGatewayProxyEvent) => {
    try {
      return await sequelize.transaction(async () => {
        const body = JSON.parse(event.body);
        const { comment } = body;
        const { pathParameters } = event;
        const id = parseInt(pathParameters?.id as string);
        console.log('updateDocumentComment');
        console.log('body: ', body);
        console.log('event: ', event);
        console.log('id', id);
        console.log('comment', comment);
        const document = await this.repository.update(id, { comment: comment });
        console.log('document', document);
        if (!document) {
          throw 'Document not found';
        }
        return new SuccessResponse(document);
      });
    } catch (error) {
      console.error(error);
      // Return an ErrorResponse with the error message and the error object.
      throw error;
    }
  };

  /**
   * Handles the update of documents without country region.
   * @returns {Promise<APIGatewayProxyResult>}
   */
  public updateCountryRegionDocument = RequestMiddleware.handle(async (): Promise<APIGatewayProxyResult> => {
    try {
      const documentRepo = new DocumentRepository();
      const documents = await documentRepo.find({
        where: {
          siteCountryRegionId: { [Op.is]: null },
        },
      });
      const results = await Promise.all(
        documents.map(async (document) => {
          // Get the coordinates of the site address
          const { siteAddressLatitude, siteAddressLongitude } = document;

          // If the coordinates are missing, skip this document
          if (!siteAddressLatitude || !siteAddressLongitude) {
            console.warn(`Missing coordinates for document ID: ${document.id}`);
            return null;
          }

          const latlng = `${formatCoordinate(Number(siteAddressLatitude))},${formatCoordinate(Number(siteAddressLongitude))}`;
          let region: string | null = null;

          try {
            // Use Google Maps API to get the region for the coordinates
            const geocodeRes = await axios.get('https://maps.googleapis.com/maps/api/geocode/json', {
              params: {
                latlng,
                key: GOOGLE_MAPS_API_KEY,
                language: 'fr',
              },
            });
            if (geocodeRes.data.status === 'OK') {
              // Extract the region from the geocode response
              const components = geocodeRes.data.results[0].address_components;
              console.log('document ID: ', document.id);
              console.log('components: ', components);
              const regionComponent = components.find((comp) => comp.types.includes('administrative_area_level_1'));
              region = regionComponent?.long_name;
            } else {
              console.warn(`Geocode failed for ${latlng}: ${geocodeRes.data.status}`);
              return null;
            }
          } catch (geoError) {
            console.error(`Geocoding error for document ID ${document.id}:`, geoError.message);
            return null;
          }
          console.log('region: ', region);
          // Find the country region from the list of available regions
          const countryRegion = COUNTRY_REGIONS.find((r) => r.name === region);
          return {
            ...document.dataValues,
            // Update the site country region ID
            siteCountryRegionId: countryRegion ? countryRegion.id : null,
          };
        }),
      );
      // Filter out the documents that failed to get a country region
      const filteredResults = results.filter((r) => r !== null);
      // Bulk update the documents with the new country region
      await documentRepo.bulkUpdate(filteredResults, { updateOnDuplicate: ['siteCountryRegionId', 'updatedAt'] });
      return new SuccessResponse(filteredResults);
    } catch (error) {
      console.error(error);
      // Return an ErrorResponse with the error message and the error object.
      return new ErrorResponse((error as Error).message, error);
    }
  });
}
