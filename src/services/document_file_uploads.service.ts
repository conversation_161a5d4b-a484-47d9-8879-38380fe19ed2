import DocumentFileUploadRepository from '../repository/document-file-upload.repository';
import { Document, DocumentFileUpload, DocumentFileUploadAttributes, DocumentFileUploadInput } from '../models';
import createDocumentFileUploadsSchema from '../schemas/DocumentFileUploads/create_document_file_uploads.schema';
import updateDocumentFileUploadsSchema from '../schemas/DocumentFileUploads/update_document_file_uploads.schema';
import { BaseService } from './base.service';
import { RequestMiddleware } from 'middy-layer';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { CriteriaOptions, ErrorResponse, Op, sequelize, SuccessResponse } from 'database-layer';
import { pick } from 'utils-layer';
import { publishZohoEvent } from './sns.service';
import DocumentRepository from '../repository/document.repository';
import { FileUploadResponse, ZohoBodyResponseEvent } from '../types';
export class DocumentFileUploadService extends BaseService<DocumentFileUpload, DocumentFileUploadInput> {
  private readonly documentFileUploadsRepository: DocumentFileUploadRepository;
  constructor() {
    super(new DocumentFileUploadRepository(), DocumentFileUploadAttributes);
    this.documentFileUploadsRepository = new DocumentFileUploadRepository();
  }

  protected getCreateSchema?(): Partial<Record<keyof DocumentFileUpload, unknown>> {
    return createDocumentFileUploadsSchema;
  }

  protected getUpdateSchema?(): Partial<Record<keyof DocumentFileUpload, unknown>> {
    return updateDocumentFileUploadsSchema;
  }

  public createUploadFile = RequestMiddleware.handle(
    async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
      try {
        return await sequelize.transaction(async () => {
          const input = pick(
            DocumentFileUploadAttributes,
            event.body as DocumentFileUploadInput,
          ) as DocumentFileUploadInput;
          const documentFileUploadRepo = new DocumentFileUploadRepository();
          const options: CriteriaOptions<Document> = {};
          if (input?.documentId) {
            options.where = {
              [Op.or]: [{ id: input?.documentId }, { booksDocumentId: input?.documentId }],
            };
          }
          const document = await new DocumentRepository().findOneByCriteria({}, options);
          if (!document || !document?.booksDocumentId) {
            throw new Error('SaleOrder not synced with Zoho');
          }
          //check if file is already exists name
          const isExist = await documentFileUploadRepo.findOne({
            where: {
              name: input?.name,
              documentId: document?.id,
            },
          });
          const uploadFile = isExist ? isExist : await documentFileUploadRepo.create(input);
          await publishZohoEvent('CREATE', uploadFile, { saleOrderId: document?.booksDocumentId });
          return new SuccessResponse(uploadFile);
        });
      } catch (error) {
        console.log(error);
        return new ErrorResponse((error as Error).message, error);
      }
    },
    createDocumentFileUploadsSchema,
  );

  public deleteUploadFile = RequestMiddleware.handle(
    async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
      return await sequelize.transaction(async () => {
        const documentFileUploadRepo = new DocumentFileUploadRepository();
        const { pathParameters } = event;
        const id = parseInt(pathParameters?.id as string);
        const fileUpLoad = await documentFileUploadRepo.findById(id);
        if (!fileUpLoad) {
          throw new Error('File not found!');
        }
        const options: CriteriaOptions<Document> = {};
        if (fileUpLoad?.documentId) {
          options.where = {
            [Op.or]: [{ id: fileUpLoad?.documentId }, { booksDocumentId: fileUpLoad?.documentId }],
          };
        }
        const document = await new DocumentRepository().findOneByCriteria({}, options);
        if (!document || !document?.booksDocumentId) {
          throw new Error('SaleOrder not synced with Zoho');
        }
        const result = await documentFileUploadRepo.deleteById(id);
        await publishZohoEvent('DELETE', fileUpLoad, { saleOrderId: document?.booksDocumentId });
        return new SuccessResponse(result);
      });
    },
  );

  public updateBooksFileId = async (event: ZohoBodyResponseEvent): Promise<void> => {
    try {
      return await sequelize.transaction(async () => {
        const documentId = event.requestData.data.DocumentFileUploads?.[0]?.documentId;
        const documentFileUpload = event.responseData.documents as FileUploadResponse[];
        await Promise.all(
          documentFileUpload?.map((fileUpload) => {
            return this.documentFileUploadsRepository.updateWhere(
              {
                documentId: documentId,
                name: fileUpload.file_name,
              },
              {
                booksFileId: fileUpload.document_id,
              },
            );
          }),
        );
      });
    } catch (error) {
      console.error(error);
      throw error;
    }
  };
}
const service = new DocumentFileUploadService();
export default service;
