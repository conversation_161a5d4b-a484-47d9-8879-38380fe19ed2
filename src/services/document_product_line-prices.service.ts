import {
  DocumentProductLinePrice,
  DocumentProductLinePriceAttributes,
  DocumentProductLinePriceInput,
} from '../models/DocumentProductLinePrice';
import { BaseService } from './base.service';
import DocumentProductLinePriceRepository from '../repository/document_product_line-prices.repository';
import {
  createDocumentProductLinePriceSchema,
  updateDocumentProductLinePriceSchema,
} from '../schemas/DocumentProductLinePrice';
export class DocumentProductLinePriceService extends BaseService<
  DocumentProductLinePrice,
  DocumentProductLinePriceInput
> {
  constructor() {
    super(new DocumentProductLinePriceRepository(), DocumentProductLinePriceAttributes);
  }

  protected getCreateSchema(): Partial<Record<keyof DocumentProductLinePrice, unknown>> {
    return createDocumentProductLinePriceSchema;
  }

  protected getUpdateSchema(): Partial<Record<keyof DocumentProductLinePrice, unknown>> {
    return updateDocumentProductLinePriceSchema;
  }
}

const service = new DocumentProductLinePriceService();
export default service;
