import {
  createDocumentProductLinePrestationSchema,
  updateDocumentProductLinePrestationSchema,
} from '../schemas/DocumentProductLinePrestation';
import {
  DocumentProductLinePrestation,
  DocumentProductLinePrestationAttributes,
  DocumentProductLinePrestationInput,
} from '../models/DocumentProductLinePrestation';
import { BaseService } from './base.service';
import DocumentProductLinePrestationRepository from '../repository/document_product_line_prestation.repository';

export class DocumentProductLinePrestationService extends BaseService<
  DocumentProductLinePrestation,
  DocumentProductLinePrestationInput
> {
  constructor() {
    super(new DocumentProductLinePrestationRepository(), DocumentProductLinePrestationAttributes);
  }

  protected getCreateSchema(): Partial<Record<keyof DocumentProductLinePrestation, unknown>> {
    return createDocumentProductLinePrestationSchema;
  }

  protected getUpdateSchema(): Partial<Record<keyof DocumentProductLinePrestation, unknown>> {
    return updateDocumentProductLinePrestationSchema;
  }
}

const service = new DocumentProductLinePrestationService();
export default service;
