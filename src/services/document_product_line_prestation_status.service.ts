import DocumentProductLinePrestationStatusRepository from '../repository/document_product_line_prestation_status.repository';
import {
  createDocumentProductLinePrestationStatusSchema,
  updateDocumentProductLinePrestationStatusSchema,
} from '../schemas/DocumentProductLinePrestationStatus';
import {
  DocumentProductLinePrestationStatus,
  DocumentProductLinePrestationStatusAttributes,
  DocumentProductLinePrestationStatusInput,
} from '../models';
import { BaseService } from './base.service';
export class DocumentProductLinePrestationStatusService extends BaseService<
  DocumentProductLinePrestationStatus,
  DocumentProductLinePrestationStatusInput
> {
  constructor() {
    super(new DocumentProductLinePrestationStatusRepository(), DocumentProductLinePrestationStatusAttributes);
  }

  protected getCreateSchema(): Partial<Record<keyof DocumentProductLinePrestationStatus, unknown>> {
    return createDocumentProductLinePrestationStatusSchema;
  }

  protected getUpdateSchema(): Partial<Record<keyof DocumentProductLinePrestationStatus, unknown>> {
    return updateDocumentProductLinePrestationStatusSchema;
  }
}

const service = new DocumentProductLinePrestationStatusService();
export default service;
