import DocumentProductLineRepository from '../repository/document_product_lines.repository';
import { createDocumentProductLineSchema, updateDocumentProductLineSchema } from '../schemas/DocumentProductLine';
import {
  DocumentProductLine,
  DocumentProductLineAttributes,
  DocumentProductLineInput,
} from '../models/DocumentProductLine';
import { BaseService } from './base.service';
export class DocumentProductLinesService extends BaseService<DocumentProductLine, DocumentProductLineInput> {
  constructor() {
    super(new DocumentProductLineRepository(), DocumentProductLineAttributes);
  }

  protected getCreateSchema(): Partial<Record<keyof DocumentProductLine, unknown>> {
    return createDocumentProductLineSchema;
  }

  protected getUpdateSchema(): Partial<Record<keyof DocumentProductLine, unknown>> {
    return updateDocumentProductLineSchema;
  }
}

const service = new DocumentProductLinesService();
export default service;
