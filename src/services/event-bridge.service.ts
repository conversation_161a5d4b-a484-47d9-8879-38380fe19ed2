import {
  EventBridgeClient,
  PutEventsCommand,
  PutEventsCommandInput,
  PutEventsRequestEntry,
} from '@aws-sdk/client-eventbridge';
import { REGION } from '../constant';
import { ZohoEventSource } from '../types';

export const client = new EventBridgeClient({ region: REGION });

export const putZohoEvent = async (
  entries: Omit<PutEventsRequestEntry & { Source: ZohoEventSource }, 'EventBusName'>[],
) => {
  const input: PutEventsCommandInput = {
    Entries: entries.map((entry) => ({
      ...entry,
      EventBusName: process.env.EVENT_BUS_NAME,
    })),
  };
  const command = new PutEventsCommand(input);
  try {
    const data = await client.send(command);
    console.log('Event sent successfully:', data);
  } catch (err) {
    console.error('Error sending event:', err);
  }
};
