import {
  CompletedPart,
  CompleteMultipartUploadCommand,
  CompleteMultipartUploadCommandInput,
  CreateMultipartUploadCommand,
  CreateMultipartUploadCommandInput,
  GetObjectCommand,
  PutObjectCommand,
  PutObjectCommandInput,
  S3Client,
  Upload<PERSON>artCommand,
  UploadPartCommandInput,
} from '@aws-sdk/client-s3';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { ErrorResponse, SuccessResponse } from 'database-layer';
import { FILE_PATH, IMAGE_UPLOAD_BUCKET, ORIGINAL_PATH, S3_REGION } from '../constant';
import parser, { MultipartFile } from 'lambda-multipart-parser';
import { getBufferFromS3Data, getObjectFromS3 } from './s3.service';
import { sync } from 'probe-image-size';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { RequestMiddleware } from 'middy-layer';

const s3 = new S3Client({
  region: S3_REGION,
});

/**
 * Generates a path based on the current date in the format: `year/month/day`
 *
 * @returns A string representing the current date as a path.
 */
const _getCurrentDatePath = (): string => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');

  return `${year}/${month}/${day}`;
};

/**
 * Factorized code from startMultipartUpload() for multipart uploads with custom path
 *
 * @param event - The API Gateway event object.
 * @returns A promise that resolves to the API Gateway proxy result.
 */

const startMultipartUploadFactorized = async (keyFile: string, fileType: string) => {
  const params: CreateMultipartUploadCommandInput = {
    Bucket: IMAGE_UPLOAD_BUCKET,
    Key: keyFile,
    ContentType: fileType,
  };
  const command = new CreateMultipartUploadCommand(params);
  return s3.send(command);
};

/**
 * Generates a UploadId for multipart upload.
 *
 * @param event - The API Gateway event object.
 * @returns A promise that resolves to the API Gateway proxy result.
 */
const startMultipartUpload = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const { fileName, fileType } = event.queryStringParameters;
    const currentDatePath = _getCurrentDatePath();
    const timestamp = new Date().getTime();
    const keyFile = `qbo/${ORIGINAL_PATH}/${currentDatePath}/${timestamp}-${fileName}`;

    const uploadData = await startMultipartUploadFactorized(keyFile, fileType);

    return new SuccessResponse({
      uploadId: uploadData.UploadId,
      keyFile: keyFile,
    });
  } catch (error) {
    console.log(error);
    // Return an error response with the error message and stack trace
    return new ErrorResponse((error as Error).message, error);
  }
};

/**
 * Saves the given file into S3.
 *
 * @param file - The file to save.
 * @param filename - The filename to save the file with.
 * @param filePath - The file path to save the file into.
 * @returns A promise that resolves to the API Gateway proxy result.
 */
const saveFileIntoS3 = async (
  file: MultipartFile,
  filename: string,
  filePath?: string,
): Promise<APIGatewayProxyResult> => {
  try {
    if (!file) throw new ErrorResponse('No files to save');
    if (!filePath) {
      filePath = '';
    }
    const keyFile = `qbo/${filePath}${filename}`;
    const params = {
      Bucket: IMAGE_UPLOAD_BUCKET,
      Key: keyFile,
      Body: file.content,
      ContentType: file.contentType,
    };
    const command = new PutObjectCommand(params);
    await s3.send(command);
    const url = `${FILE_PATH}/${keyFile}`;
    const response = {
      fileURL: url,
      fileName: file.filename,
    };
    return new SuccessResponse(response);
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Updates a file into S3.
 *
 * @param event - The API Gateway event object.
 * @returns A promise that resolves to the API Gateway proxy result.
 */
const updateFileIntoS3 = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    // Parse the event to extract the file
    const result = await parser.parse(event);
    if (!result.files?.length) throw new ErrorResponse('File not found');
    const file = result.files[0];

    // Generate the key for the file in S3
    const currentDatePath = _getCurrentDatePath();
    const timestamp = new Date().getTime();
    const keyFile = `qbo/${ORIGINAL_PATH}/${currentDatePath}/${timestamp}-${file.filename}`;

    // Save the file to S3
    const params = {
      Bucket: IMAGE_UPLOAD_BUCKET,
      Key: keyFile,
      Body: file.content,
      ContentType: file.contentType,
    };
    const command = new PutObjectCommand(params);
    await s3.send(command);

    // Get the object from S3 to extract the image dimensions
    const object = await getObjectFromS3(keyFile);
    const buffer = await getBufferFromS3Data(object);
    const dimensions = sync(buffer);

    // Return the image URL and its dimensions
    return new SuccessResponse({
      imageURL: `${FILE_PATH}/${keyFile}`,
      fileName: file.filename,
      width: dimensions.width,
      height: dimensions.height,
    });
  } catch (error) {
    console.log(error);
    // Return an error response with the error message and stack trace
    return new ErrorResponse((error as Error).message, error);
  }
};

/**
 * Generates a pre-signed URL for uploading large files to S3.
 *
 * @param event - The API Gateway event object.
 * @returns A promise that resolves to the API Gateway proxy result.
 */
const generateSignedUploadUrl = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const { fileName, fileType } = event.queryStringParameters;
    const currentDatePath = _getCurrentDatePath();
    const timestamp = new Date().getTime();
    const keyFile = `qbo/${ORIGINAL_PATH}/${currentDatePath}/${timestamp}-${fileName}`;
    const params: PutObjectCommandInput = {
      Bucket: IMAGE_UPLOAD_BUCKET,
      Key: keyFile,
      ContentType: fileType,
    };
    const command = new PutObjectCommand(params);
    const preSignedURL = await getSignedUrl(s3, command, {
      expiresIn: 60 * 60,
    });

    return new SuccessResponse({
      keyFile,
      fileUrl: `${FILE_PATH}/${keyFile}`,
      preSignedURL,
    });
  } catch (error) {
    console.log(error);
    // Return an error response with the error message and stack trace
    return new ErrorResponse((error as Error).message, error);
  }
};

/**
 * Generates a pre-signed URL for uploading large files to S3.
 *
 * @param event - The API Gateway event object.
 * @returns A promise that resolves to the API Gateway proxy result.
 */
const generateSignedMultipartUpload = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const { keyFile, partNumber, uploadId } = event.queryStringParameters;
    const params: UploadPartCommandInput = {
      Bucket: IMAGE_UPLOAD_BUCKET,
      Key: keyFile,
      PartNumber: parseInt(partNumber),
      UploadId: uploadId,
    };
    const command = new UploadPartCommand(params);
    const preSignedURL = await getSignedUrl(s3, command, {
      expiresIn: 60 * 5,
    });

    return new SuccessResponse({
      preSignedURL,
    });
  } catch (error) {
    console.log(error);
    // Return an error response with the error message and stack trace
    return new ErrorResponse((error as Error).message, error);
  }
};

/**
 * Factorized part of completeMultipartUpload() for implementation from other services, allowing action to be made on upload finish.
 *
 * @param event - The API Gateway event object.
 * @returns A promise that resolves to the API Gateway proxy result.
 */
const completeMultipartUploadFactorized = (keyFile: string, parts: CompletedPart[], uploadId: string) => {
  const params: CompleteMultipartUploadCommandInput = {
    Bucket: IMAGE_UPLOAD_BUCKET,
    Key: keyFile,
    MultipartUpload: {
      Parts: parts,
    },
    UploadId: uploadId,
  };
  const command = new CompleteMultipartUploadCommand(params);
  return s3.send(command);
};

/**
 * Completes a multipart upload.
 *
 * @param event - The API Gateway event object.
 * @returns A promise that resolves to the API Gateway proxy result.
 */
const completeMultipartUpload = RequestMiddleware.handle(
  async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
      const { keyFile, parts, uploadId } = event.body as unknown as {
        keyFile: string;
        parts: CompletedPart[];
        uploadId: string;
      };
      const uploadData = await completeMultipartUploadFactorized(keyFile, parts, uploadId);
      return new SuccessResponse({
        keyFile,
        fileUrl: `${FILE_PATH}/${keyFile}`,
        data: uploadData,
      });
    } catch (error) {
      console.log(error);
      // Return an error response with the error message and stack trace
      return new ErrorResponse((error as Error).message, error);
    }
  },
);

/**
 * Gets a pre-signed URL for accessing a file in S3 securely.
 *
 * @param filePath - The path of the file in S3.
 * @returns A promise that resolves to the pre-signed URL.
 */
const getURLFromS3 = async (filePath: string): Promise<string> => {
  try {
    const keyFile = `qbo/${filePath}`;
    const params = {
      // The name of the bucket to get the file from
      Bucket: IMAGE_UPLOAD_BUCKET,
      // The key of the file to get
      Key: keyFile,
      // The number of seconds before the pre-signed URL expires
      Expires: 60,
    };
    const command = new GetObjectCommand(params);
    // Get the pre-signed URL
    const preSignedURL = await getSignedUrl(s3, command);
    return preSignedURL;
  } catch (error) {
    // Throw an error with the error message
    throw new Error(error.message);
  }
};

/**
 * Generates a pre-signed URL for accessing files in S3 securely.
 *
 * @param event - The API Gateway event object.
 * @returns A promise that resolves to the API Gateway proxy result.
 */
const generateSignedGet = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const { keyFile } = event.queryStringParameters;

    const preSignedURL = await getURLFromS3(keyFile);

    return new SuccessResponse({
      preSignedURL,
    });
  } catch (error) {
    console.log(error);
    // Return an error response with the error message and stack trace
    return new ErrorResponse((error as Error).message, error);
  }
};

export {
  updateFileIntoS3,
  saveFileIntoS3,
  startMultipartUpload,
  startMultipartUploadFactorized,
  generateSignedUploadUrl,
  generateSignedMultipartUpload,
  completeMultipartUpload,
  generateSignedGet,
};
