import { LambdaClient, InvokeCommand, InvokeCommandInput } from '@aws-sdk/client-lambda';

const lambdaClient = new LambdaClient();

export const invokeLambda = async <T>(request: Pick<InvokeCommandInput, 'FunctionName' | 'Payload'>): Promise<T> => {
  const command = new InvokeCommand({
    ...request,
    InvocationType: 'RequestResponse',
  });
  try {
    const response = await lambdaClient.send(command);
    console.log('response', response);
    const responsePayload = response.Payload ? JSON.parse(Buffer.from(response.Payload).toString('utf-8')) : null;

    console.log('Lambda Response:', responsePayload);
    if (response.FunctionError || responsePayload?.errorType === 'Error') {
      throw new Error(responsePayload?.errorMessage);
    }
    return responsePayload;
  } catch (error) {
    console.error('Error invoking Lambda:', error);
    throw error;
  }
};
