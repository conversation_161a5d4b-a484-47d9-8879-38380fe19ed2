import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { ErrorResponse, SuccessResponse } from 'database-layer';
import { 
  CreateProviderInvoiceRequest,
  UpdateProviderInvoiceRequest
} from '../types/provider-invoice.types';
import { invoiceList, commandePrestaCreate, commandePrestaEdit } from '../mockData';

export class ProviderInvoiceService {
  constructor() {
    // Using mock data - no database needed
  }

  /**
   * Get list of provider invoices - using mock data
   */
  public getInvoicesList = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
      const queryStringParams = event.queryStringParameters || {};
      const { 
        page = '1', 
        limit = '10', 
        prestataire, 
        status 
      } = queryStringParams;

      // Add small delay to simulate API call
      await new Promise(resolve => setTimeout(resolve, 300));

      // Filter mock data based on query parameters
      let filteredData = [...invoiceList];

      if (prestataire) {
        filteredData = filteredData.filter(invoice => 
          invoice.prestataire.toLowerCase().includes(prestataire.toLowerCase())
        );
      }

      if (status) {
        filteredData = filteredData.filter(invoice => invoice.status === status);
      }

      // Simple pagination
      const pageNum = parseInt(page);
      const limitNum = parseInt(limit);
      const startIndex = (pageNum - 1) * limitNum;
      const endIndex = startIndex + limitNum;
      const paginatedData = filteredData.slice(startIndex, endIndex);

      return new SuccessResponse({
        rows: paginatedData,
        count: filteredData.length,
        page: pageNum,
        limit: limitNum
      });
    } catch (error) {
      console.log('Error in getInvoicesList:', error);
      return new ErrorResponse((error as Error).message, error);
    }
  };

  /**
   * Get provider invoices by provider ID - using mock data
   */
  public getInvoicesByProviderId = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
      const { providerId } = event.pathParameters || {};
      
      if (!providerId) {
        return new ErrorResponse('Provider ID is required', null);
      }

      // Add small delay to simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      // Filter invoices by provider ID
      const filteredInvoices = invoiceList.filter(invoice => 
        invoice.prestataire.toLowerCase().includes(providerId.toLowerCase())
      );

      return new SuccessResponse({
        rows: filteredInvoices,
        count: filteredInvoices.length
      });
    } catch (error) {
      console.log('Error in getInvoicesByProviderId:', error);
      return new ErrorResponse((error as Error).message, error);
    }
  };

  /**
   * Get invoice creation data - using mock data (similar to commandePrestaCreate)
   */
  public getInvoiceCreationData = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
      const queryStringParams = event.queryStringParameters || {};
      const { prestataire } = queryStringParams;

      if (!prestataire) {
        return new ErrorResponse('Prestataire parameter is required', null);
      }

      // Add small delay to simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      console.log(`🔄 Service: CREATE - Fetching creation data for ${prestataire}`);

      // Return mock creation data
      return new SuccessResponse(commandePrestaCreate);
    } catch (error) {
      console.log('Error in getInvoiceCreationData:', error);
      return new ErrorResponse((error as Error).message, error);
    }
  };

  /**
   * Create a new provider invoice - using mock data
   */
  public createInvoice = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
      const requestBody: CreateProviderInvoiceRequest = JSON.parse(event.body || '{}');
      
      if (!requestBody.prestataire) {
        return new ErrorResponse('Prestataire is required', null);
      }

      // Add small delay to simulate API call
      await new Promise(resolve => setTimeout(resolve, 800));

      // Generate a mock ID
      const newId = Math.max(...invoiceList.map(i => i.id)) + 1;

      // Create mock response
      const newInvoice = {
        id: newId,
        createdAt: new Date().toISOString(),
        createdBy: 'System',
        prestataire: requestBody.prestataire,
        prestataireInvoiceNumber: requestBody.prestataireInvoiceNumber || `FAC-${Date.now()}`,
        status: requestBody.status || 'draft',
        totalSelected: requestBody.totalSelected || 0,
        totalAvoirs: 0,
        totalAmount: requestBody.totalAmount || 0,
        invoicedAmount: requestBody.totalAmount || 0
      };

      // Add to mock data (in memory only)
      invoiceList.push(newInvoice);

      return new SuccessResponse({
        id: newId,
        message: 'Provider invoice created successfully',
        data: newInvoice
      });
    } catch (error) {
      console.log('Error in createInvoice:', error);
      return new ErrorResponse((error as Error).message, error);
    }
  };

  /**
   * Update an existing provider invoice - using mock data
   */
  public updateInvoice = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
      const { id } = event.pathParameters || {};
      const requestBody: UpdateProviderInvoiceRequest = JSON.parse(event.body || '{}');
      
      if (!id) {
        return new ErrorResponse('Invoice ID is required', null);
      }

      // Add small delay to simulate API call
      await new Promise(resolve => setTimeout(resolve, 600));

      // Find invoice in mock data
      const invoiceIndex = invoiceList.findIndex(invoice => invoice.id === parseInt(id));
      if (invoiceIndex === -1) {
        return new ErrorResponse('Invoice not found', null);
      }

      // Update mock data
      const updatedInvoice = {
        ...invoiceList[invoiceIndex],
        ...requestBody,
        id: parseInt(id) // Ensure ID doesn't change
      };

      invoiceList[invoiceIndex] = updatedInvoice;

      return new SuccessResponse({
        id: parseInt(id),
        message: 'Provider invoice updated successfully',
        data: updatedInvoice
      });
    } catch (error) {
      console.log('Error in updateInvoice:', error);
      return new ErrorResponse((error as Error).message, error);
    }
  };

  /**
   * Get invoice details for editing (similar to commandePrestaEdit)
   */
  public getInvoiceDetailsEdit = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
      const { id } = event.pathParameters || {};
      const queryStringParams = event.queryStringParameters || {};
      const { prestataire } = queryStringParams;
      
      if (!id && !prestataire) {
        return new ErrorResponse('Invoice ID or prestataire is required', null);
      }

      // Add small delay to simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      console.log(`🔄 Service: EDIT - Fetching invoice for ${prestataire} (ID: ${id})`);

      // Return mock edit data
      return new SuccessResponse(commandePrestaEdit);
    } catch (error) {
      console.log('Error in getInvoiceDetailsEdit:', error);
      return new ErrorResponse((error as Error).message, error);
    }
  };
}
