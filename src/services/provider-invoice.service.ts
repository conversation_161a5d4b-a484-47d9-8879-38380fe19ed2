import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { ErrorResponse, SuccessResponse, Op } from 'database-layer';
import DocumentRepository from '../repository/document.repository';
import { Document } from '../models';
import { DOCUMENT_TYPES } from '../constant';
import {
  ProviderInvoiceListItem,
  CreateProviderInvoiceRequest,
  UpdateProviderInvoiceRequest
} from '../types/provider-invoice.types';
import { invoiceList, commandePrestaCreate, commandePrestaEdit } from '../mockData';

export class ProviderInvoiceService {
  private readonly documentRepository: DocumentRepository;

  constructor() {
    this.documentRepository = new DocumentRepository();
  }

  /**
   * Get list of provider invoices - simple SQL query on documents table
   */
  public getInvoicesList = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
      const queryStringParams = event.queryStringParameters || {};
      const { 
        page = '1', 
        limit = '10', 
      } = queryStringParams;

      // // Simple where conditions for documents table
      // const whereConditions: Record<string, unknown> = {
      //   '$DocumentType.key$': DOCUMENT_TYPES.ORDER // Use existing document type
      // };

      // if (prestataire) {
      //   whereConditions.prestataire = { [Op.like]: `%${prestataire}%` };
      // }

      // if (status) {
      //   whereConditions.status = status;
      // }

      // // Simple query using existing repository
      // const result = await this.documentRepository.getDocumentsFilterQuery(
      //   limit,
      //   page,
      //   'createdAt',
      //   null,
      //   whereConditions
      // );

      // Simple transformation
      // const transformedRows: ProviderInvoiceListItem[] = result.rows.map((doc: Record<string, unknown>) => ({
      //   id: Number(doc.id),
      //   createdAt: String(doc.createdAt),
      //   createdBy: String(doc.createdBy || 'System'),
      //   prestataire: String(doc.prestataire || ''),
      //   prestataireInvoiceNumber: String(doc.prestataireInvoiceNumber || ''),
      //   status: String(doc.status || 'draft'),
      //   totalSelected: parseFloat(String(doc.totalSelected || '0')),
      //   totalAvoirs: parseFloat(String(doc.totalAvoirs || '0')),
      //   totalAmount: parseFloat(String(doc.totalAmount || '0')),
      //   invoicedAmount: parseFloat(String(doc.invoicedAmount || '0'))
      // }));

      return new SuccessResponse({
        rows: invoiceList,
        count: invoiceList.length,
        page: parseInt(page),
        limit: parseInt(limit)
      });
    } catch (error) {
      console.log('Error in getInvoicesList:', error);
      return new ErrorResponse((error as Error).message, error);
    }
  };

  /**
   * Get provider invoices by provider ID - simple SQL query
   */
  public getInvoicesByProviderId = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
      const { providerId } = event.pathParameters || {};
      console.log(providerId, "providerId");
      
      // if (!providerId) {
      //   return new ErrorResponse('Provider ID is required', null);
      // }

      // const whereConditions = {
      //   '$DocumentType.key$': DOCUMENT_TYPES.ORDER,
      //   prestataire: providerId
      // };

      // const result = await this.documentRepository.getDocumentsFilterQuery(
      //   '100',
      //   '1',
      //   'createdAt',
      //   null,
      //   whereConditions
      // );

      return new SuccessResponse({
        rows: commandePrestaEdit.rows,
        count: commandePrestaEdit.length
      });
    } catch (error) {
      console.log('Error in getInvoicesByProviderId:', error);
      return new ErrorResponse((error as Error).message, error);
    }
  };

  /**
   * Get invoice creation data - simple SQL query
   */
  public getInvoiceCreationData = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
      const queryStringParams = event.queryStringParameters || {};
      const { prestataire } = queryStringParams;

      if (!prestataire) {
        return new ErrorResponse('Prestataire parameter is required', null);
      }

      // Simple SQL query to get orders for this provider
      const whereConditions = {
        '$DocumentType.key$': DOCUMENT_TYPES.ORDER,
        prestataire: prestataire
      };

      const result = await this.documentRepository.getDocumentsFilterQuery(
        '100',
        '1',
        'createdAt',
        null,
        whereConditions
      );

      return new SuccessResponse(result.rows);
    } catch (error) {
      console.log('Error in getInvoiceCreationData:', error);
      return new ErrorResponse((error as Error).message, error);
    }
  };

  /**
   * Create a new provider invoice - simple POST with SQL insert
   */
  public createInvoice = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
      const requestBody: CreateProviderInvoiceRequest = JSON.parse(event.body || '{}');
      
      if (!requestBody.prestataire) {
        return new ErrorResponse('Prestataire is required', null);
      }

      // Simple document creation
      const documentData = {
        documentTypeId: 1, // Use existing document type ID
        prestataire: requestBody.prestataire,
        prestataireInvoiceNumber: requestBody.prestataireInvoiceNumber,
        status: requestBody.status || 'draft',
        totalAmount: requestBody.totalAmount || 0,
        totalSelected: requestBody.totalSelected || 0,
        createdBy: 'System'
      };

      const createdDocument = await Document.create(documentData);

      return new SuccessResponse({
        id: createdDocument.id,
        message: 'Provider invoice created successfully'
      });
    } catch (error) {
      console.log('Error in createInvoice:', error);
      return new ErrorResponse((error as Error).message, error);
    }
  };

  /**
   * Update an existing provider invoice - simple PUT with SQL update
   */
  public updateInvoice = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
      const { id } = event.pathParameters || {};
      const requestBody: UpdateProviderInvoiceRequest = JSON.parse(event.body || '{}');
      
      if (!id) {
        return new ErrorResponse('Invoice ID is required', null);
      }

      // Find and update document
      const document = await Document.findByPk(id);
      if (!document) {
        return new ErrorResponse('Invoice not found', null);
      }

      // Simple update - only update allowed fields
      const updateData: Record<string, unknown> = {};
      if (requestBody.prestataireInvoiceNumber) {
        updateData.prestataireInvoiceNumber = requestBody.prestataireInvoiceNumber;
      }
      if (requestBody.status) {
        updateData.status = requestBody.status;
      }
      if (requestBody.totalAmount !== undefined) {
        updateData.totalAmount = requestBody.totalAmount;
      }
      if (requestBody.totalSelected !== undefined) {
        updateData.totalSelected = requestBody.totalSelected;
      }

      await document.update(updateData);

      return new SuccessResponse({
        id: document.id,
        message: 'Provider invoice updated successfully'
      });
    } catch (error) {
      console.log('Error in updateInvoice:', error);
      return new ErrorResponse((error as Error).message, error);
    }
  };
}
