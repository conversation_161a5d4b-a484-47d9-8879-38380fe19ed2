import { S3Client, GetObjectCommand, GetObjectCommandOutput } from '@aws-sdk/client-s3';
import { Readable } from 'stream';
import { FILE_PATH, IMAGE_UPLOAD_BUCKET, S3_REGION } from '../constant';

const s3 = new S3Client({
  region: S3_REGION,
});

export const getObjectFromS3 = async (fileKey: string) => {
  const params = {
    Bucket: IMAGE_UPLOAD_BUCKET,
    Key: fileKey.replace(`${FILE_PATH}/`, ''),
  };

  // Fetch the image object from S3
  const command = new GetObjectCommand(params);
  return await s3.send(command);
};

export const getBufferFromS3Data = async (data: GetObjectCommandOutput): Promise<Buffer> => {
  if (!data.Body) {
    throw new Error('No data received from S3');
  }

  if (Buffer.isBuffer(data.Body)) {
    return data.Body;
  }

  if (data.Body instanceof Readable) {
    return new Promise<Buffer>((resolve, reject) => {
      const chunks: Buffer[] = [];
      (data.Body as NodeJS.ReadableStream).on('data', (chunk) => chunks.push(chunk));
      (data.Body as NodeJS.ReadableStream).on('error', reject);
      (data.Body as NodeJS.ReadableStream).on('end', () => resolve(Buffer.concat(chunks)));
    });
  }

  if (typeof data.Body === 'string') {
    return Buffer.from(data.Body);
  }

  throw new Error('Unsupported body type');
};
