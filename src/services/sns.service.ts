import { SNSClient, PublishCommand, PublishCommandInput, PublishCommandOutput } from '@aws-sdk/client-sns';
import { ZOHO_REQUEST_TOPIC_ARN, ZOHO_GROUP_ID, SERVICE_NAME } from '../constant';
import { v4 as uuidv4 } from 'uuid';
import { Model } from 'database-layer';

export const snsClient = new SNSClient();

export const publishZohoEvent = async <T extends Model & { id: number | string }>(
  action: 'CREATE' | 'UPDATE' | 'DELETE' | 'CREATE-ESTIMATE-AND-ORDER' | 'UPDATE-STATUS-ORDER',
  data: T,
  extraData: Record<string, unknown> = {},
): Promise<PublishCommandOutput> => {
  try {
    const messageId = uuidv4();
    const input: PublishCommandInput = {
      TopicArn: ZOHO_REQUEST_TOPIC_ARN,
      MessageDeduplicationId: messageId,
      MessageGroupId: ZOHO_GROUP_ID,
      Subject: `${action} ${data.constructor.name} ${data.id}`,
      Message: JSON.stringify({
        action,
        model: data.constructor.name,
        service: SERVICE_NAME,
        data: { ...data.toJSON(), ...extraData },
      }),
      MessageAttributes: {
        eventType: {
          DataType: 'String',
          StringValue: `${action}-${data.constructor.name}`,
        },
        actionType: {
          DataType: 'String',
          StringValue: action,
        },
        eventModel: {
          DataType: 'String',
          StringValue: data.constructor.name,
        },
      },
    };
    const command = new PublishCommand(input);
    const response = await snsClient.send(command);
    console.log('Send message to SNS', response);
    return response;
  } catch (error) {
    console.log(error);
  }
};
