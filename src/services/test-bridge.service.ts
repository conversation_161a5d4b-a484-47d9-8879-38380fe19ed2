import { APIGatewayProxyResult } from 'aws-lambda';
import { putZohoEvent } from './event-bridge.service';

const testEvent = async (): Promise<APIGatewayProxyResult> => {
  console.log('Send test event to EventBridge');
  await putZohoEvent([
    {
      DetailType: 'CreateDocument',
      Detail: JSON.stringify({ message: 'Test event for create document' }),
      Source: 'document',
    },
    {
      DetailType: 'CreateContact',
      Detail: JSON.stringify({ message: 'Test event for create contact' }),
      Source: 'contact',
    },
  ]);
  return {
    statusCode: 200,
    body: 'Sent event to EventBridge',
  };
};

export default { testEvent };
