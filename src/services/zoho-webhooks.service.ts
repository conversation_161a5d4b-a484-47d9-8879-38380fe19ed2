import { ErrorResponse, sequelize } from 'database-layer';
import DocumentStatusRepository from '../repository/document-status.repository';
import DocumentRepository from '../repository/document.repository';
import { ZohoEstimate, ZohoSalesOrder, ZohoWebhookDataEvent } from '../types';
import { Document } from '../models';

function isZohoSalesOrder(data: ZohoSalesOrder | ZohoEstimate): data is ZohoSalesOrder {
  return (data as ZohoSalesOrder).salesorder_id !== undefined;
}

/**
 * Type guard function to determine if the given data is a ZohoEstimate.
 *
 * @param {ZohoSalesOrder | ZohoEstimate} data - The data object to be checked.
 * @returns {boolean} - Returns true if the data is a ZohoEstimate; otherwise, false.
 */
function isZohoEstimate(data: ZohoSalesOrder | ZohoEstimate): data is ZohoEstimate {
  return (data as ZohoEstimate).estimate_id !== undefined;
}

/**
 * Handles the webhook event from Zoho and updates the document status.
 *
 * @param {ZohoWebhookDataEvent} event - The event data from Zoho webhook.
 * @returns {Promise<{ document: Document }>} - The updated document.
 * @throws {ErrorResponse} - Throws an error response in case of failure.
 */
export const webhookDocument = async (event: ZohoWebhookDataEvent): Promise<{ document: Document }> => {
  try {
    console.log('webhookDocument event: ', event);

    // Extract document data from the event, prioritizing sales order over estimate
    const documentData =
      (event.requestData?.salesorder as unknown as ZohoSalesOrder) ||
      (event.requestData?.estimate as unknown as ZohoEstimate);
    console.log('documentData: ', documentData);

    if (documentData) {
      let booksDocumentId: string;
      let zohoStatus: string;
      let zohoCurrentSubStatus: string;

      // Determine if the document is a sales order or an estimate
      if (isZohoSalesOrder(documentData)) {
        booksDocumentId = documentData.salesorder_id;
        zohoStatus = documentData.status;
        zohoCurrentSubStatus = documentData.current_sub_status;
      } else if (isZohoEstimate(documentData)) {
        booksDocumentId = documentData.estimate_id;
        zohoStatus = documentData.status;
        zohoCurrentSubStatus = documentData.current_sub_status;
      } else {
        throw new Error('Unknown document type');
      }

      console.log('Book Document ID: ', booksDocumentId);

      if (booksDocumentId) {
        const documentRepository = new DocumentRepository();
        const documentStatusRepository = new DocumentStatusRepository();

        // Retrieve document status from the repository based on Zoho status
        const documentStatus = await documentStatusRepository.findOneOrFail({
          where: {
            zohoStatus,
            zohoCurrentSubStatus,
          },
        });
        console.log('Document Status: ', documentStatus);

        // Find the document by booksDocumentId
        const document = await documentRepository.findOneOrFail({
          where: {
            booksDocumentId,
          },
        });
        console.log('Document: ', document);

        // Update the document with new status and custom fields
        await document.update({
          documentStatusId: documentStatus.id,
          demandeCommerciale: documentData?.custom_field_hash?.cf_demande_commerciale ?? null,
          responseLogistique: documentData?.custom_field_hash?.cf_r_ponse_logistique ?? null,
          retainerinvoiceId: documentData?.custom_field_hash?.cf_retainerinvoice_id ?? null,
        });
        console.log('documentSync', document);

        return {
          document,
        };
      }
    }
  } catch (error) {
    console.error('Error in webhookDocument:', error);
    throw new ErrorResponse((error as Error).message, error);
  }
};

export const updateDocument = async (event: ZohoWebhookDataEvent): Promise<void> => {
  try {
    return await sequelize.transaction(async () => {
      const document = event.requestData as unknown as Document;
      console.log('zoho-document', document);
      const documentRepo = new DocumentRepository();
      if (!document?.booksDocumentId) {
        throw new Error('booksDocumentId is required');
      }
      const documentToUpdate = await documentRepo.findOneOrFailByCriteria({
        booksDocumentId: document?.booksDocumentId,
      });
      if (!documentToUpdate) {
        throw new Error('Document not found');
      }
      const rs = await documentRepo.update(documentToUpdate.id, document);
      console.log('updateDocumentFromZoho:>>>>>>>>', rs);
    });
  } catch (error) {
    console.error('Error in updateDocument:', error);
    throw new ErrorResponse((error as Error).message, error);
  }
};
