import { APIGatewayProxyResult, Context } from 'aws-lambda';
import { describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import { sequelize } from 'database-layer'; // Adjust import according to your setup
import { generateApiGatewayEvent } from './mock-events'; // Adjust path as necessary

export abstract class BaseUnitTest {
  protected abstract createTestData(): Promise<void>;
  protected abstract clearTestData(): Promise<void>;
  protected abstract handler(event: any, context: Context): Promise<APIGatewayProxyResult>;
  protected abstract serviceName: string;
  protected testData: any = {}; // Property to hold external test data

  constructor(testData: any = {}) {
    this.testData = testData;
  }

  protected generateEvent(method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE', path: string, body?: any): any {
    return generateApiGatewayEvent({ path, httpMethod: method, body }); // Use the provided event generator
  }

  describeTests(options?: {
    runListTest?: boolean;
    runGetByIdTest?: boolean;
    runCreateTest?: boolean;
    runUpdateTest?: boolean;
    runDeleteTest?: boolean;
    customPaths?: {
      listPath?: string;
      getByIdPath?: string;
      createPath?: string;
      updatePath?: string;
      deletePath?: string;
    };
  }) {
    const context: Context = {} as Context;
    const defaults = {
      runListTest: true,
      runGetByIdTest: true,
      runCreateTest: true,
      runUpdateTest: true,
      runDeleteTest: true,
    };
    const opts = { ...defaults, ...options };

    const defaultPaths = {
      listPath: `/${this.serviceName}`,
      getByIdPath: `/${this.serviceName}/:id`,
      createPath: `/${this.serviceName}`,
      updatePath: `/${this.serviceName}/:id`,
      deletePath: `/${this.serviceName}/:id`,
    };

    const paths = { ...defaultPaths, ...options?.customPaths };

    describe(`${this.serviceName} API Handler`, () => {
      beforeAll(async (): Promise<void> => {
        try {
          await this.createTestData();
        } catch (error) {
          console.log(error);
        }
      });

      afterAll(async (): Promise<void> => {
        try {
          await this.clearTestData();
          await sequelize.close();
        } catch (error) {
          console.log(error);
        }
      });
      if (opts.runListTest) {
        it('should fetch a list of entities', async (): Promise<void> => {
          const event = this.generateEvent('GET', paths.listPath);
          const result: APIGatewayProxyResult = await this.handler(event, context);
          expect(result.statusCode).toEqual(200);

          const body = JSON.parse(result.body);
          expect(Array.isArray(body.rows)).toBe(true);
          body.rows.forEach((item: any) => {
            expect(item).toHaveProperty('id');
          });
        });
      }
      if (opts.runGetByIdTest) {
        it('should fetch a single entity by ID', async (): Promise<void> => {
          const id = this.testData?.entityId; // Use external test data
          const event = this.generateEvent('GET', paths.getByIdPath.replace(':id', `${id}`));
          const result: APIGatewayProxyResult = await this.handler(event, context);
          expect(result.statusCode).toEqual(200);

          const body = JSON.parse(result.body);
          expect(body).toHaveProperty('id', id);
        });
        it('should not fetch a single entity that does not exist', async (): Promise<void> => {
          const id = 9999;
          const event = this.generateEvent('GET', paths.getByIdPath.replace(':id', `${id}`));
          const result: APIGatewayProxyResult = await this.handler(event, context);
          expect(result.statusCode).toEqual(404);
        });
      }
      if (opts.runCreateTest) {
        it('should create a new entity', async (): Promise<void> => {
          const input = this.testData?.newData; // Use external test data
          if (input) {
            const event = this.generateEvent('POST', paths.createPath, input);
            const result: APIGatewayProxyResult = await this.handler(event, context);
            expect(result.statusCode).toEqual(200);

            const body = JSON.parse(result.body);
            expect(body).toMatchObject({ ...input });
          }
        });
        it('should not create a new entity with missing required fields', async (): Promise<void> => {
          const input = {};
          if (input) {
            const event = this.generateEvent('POST', paths.createPath, input);
            const result: APIGatewayProxyResult = await this.handler(event, context);
            expect(result.statusCode).toEqual(400);
          }
        });
      }
      if (opts.runUpdateTest) {
        it('should update an existing entity', async (): Promise<void> => {
          const id = this.testData?.entityId; // Use external test data
          const input = this.testData?.updateData; // Use external test data
          const event = this.generateEvent('PUT', paths.updatePath.replace(':id', `${id}`), input);
          const result: APIGatewayProxyResult = await this.handler(event, context);
          expect(result.statusCode).toEqual(200);

          const body = JSON.parse(result.body);
          expect(body).toMatchObject({ ...input });
        });
        it('should not update an existing entity with missing required fields', async (): Promise<void> => {
          const id = this.testData?.entityId; // Use external test data
          const input = {};
          if (input) {
            const event = this.generateEvent('PUT', paths.updatePath.replace(':id', `${id}`), input);
            const result: APIGatewayProxyResult = await this.handler(event, context);
            expect(result.statusCode).toEqual(400);
          }
        });
      }
      if (opts.runDeleteTest) {
        it('should delete an entity', async (): Promise<void> => {
          const id = this.testData.entityId; // Use external test data
          const event = this.generateEvent('DELETE', paths.deletePath.replace(':id', `${id}`));
          const result: APIGatewayProxyResult = await this.handler(event, {} as Context);
          expect(result.statusCode).toEqual(200);

          const body = JSON.parse(result.body);
          expect(body).toMatchObject({
            isSuccess: true,
            message: 'Entity deleted successfully',
          });
        });
      }
    });
  }
}
