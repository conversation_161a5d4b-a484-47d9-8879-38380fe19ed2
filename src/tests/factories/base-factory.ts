import { Model, ModelStatic, MakeNullishOptional } from 'database-layer';

export default class BaseFactory<
  M extends Model,
  MInput extends Partial<MakeNullishOptional<M['_creationAttributes']>>,
> {
  protected input: MakeNullishOptional<M['_creationAttributes']>;
  protected readonly model!: ModelStatic<M>;

  constructor(model: ModelStatic<M>, defaultProps: any, props?: MInput) {
    this.model = model;
    this.input = Object.assign({}, defaultProps, props) as MakeNullishOptional<M['_creationAttributes']>;
  }

  make() {
    return this.input;
  }

  async create() {
    return await this.model.create(this.input);
  }

  async delete() {
    return await this.model.destroy(this.input);
  }
}
