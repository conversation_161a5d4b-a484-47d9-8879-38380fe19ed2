import { Contact, ContactInput } from './../../models/readonly/Contact';
import { faker } from '@faker-js/faker';
import BaseFactory from './base-factory';

class ContactFact extends BaseFactory<Contact, Partial<ContactInput>> {
  constructor(props?: Partial<ContactInput>) {
    const defaultProps: Partial<ContactInput> = {
      clientType: 'business',
      name: faker.company.name(),
      siren: faker.company.name(),
      enCompte: faker.company.name(),
      typeDeCompte: faker.company.name(),
      crmOwnerId: faker.company.name(),
      crmAccountId: faker.company.name(),
      crmContactId: faker.company.name(),
      booksContactId: faker.company.name(),
      encoursAut: faker.company.name(),
      encoursDispo: faker.company.name(),
      estimatesM: faker.company.name(),
      sales: faker.company.name(),
      estimates: faker.company.name(),
      orders: faker.company.name(),
      invoices: faker.company.name(),
    };
    super(Contact, defaultProps, props);
  }
}

export default function ContactFactory(input?: Partial<ContactInput>): ContactFact {
  return new ContactFact(input);
}
