import { faker } from '@faker-js/faker';
import BaseFactory from './base-factory';
import { DocumentCCContact, DocumentCCContactInput } from '../../models';

class DocumentCCContactFact extends BaseFactory<DocumentCCContact, Partial<DocumentCCContactInput>> {
  constructor(props?: Partial<DocumentCCContactInput>) {
    const defaultProps: Partial<DocumentCCContactInput> = {
      contactPersonId: faker.number.int({ min: 1, max: 10 }),
    };
    super(DocumentCCContact, defaultProps, props);
  }
}

export default function DocumentCCContactFactory(input?: Partial<DocumentCCContactInput>): DocumentCCContactFact {
  return new DocumentCCContactFact(input);
}
