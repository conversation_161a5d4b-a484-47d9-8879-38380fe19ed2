import { faker } from '@faker-js/faker';
import BaseFactory from './base-factory';
import { DocumentCCLibresContact, DocumentCCLibresContactInput } from '../../models';

class DocumentCCLibresContactFact extends BaseFactory<DocumentCCLibresContact, Partial<DocumentCCLibresContactInput>> {
  constructor(props?: Partial<DocumentCCLibresContactInput>) {
    const defaultProps: Partial<DocumentCCLibresContactInput> = {
      email: faker.person.fullName + '@gmail.com',
    };
    super(DocumentCCLibresContact, defaultProps, props);
  }
}

export default function DocumentCCLibresContactFactory(
  input?: Partial<DocumentCCLibresContactInput>,
): DocumentCCLibresContactFact {
  return new DocumentCCLibresContactFact(input);
}
