import { faker } from '@faker-js/faker';
import BaseFactory from './base-factory';
import { DocumentProductLinePrestationStatus, DocumentProductLinePrestationStatusInput } from '../../models';

class DocumentProductLinePrestationStatusFact extends BaseFactory<
  DocumentProductLinePrestationStatus,
  Partial<DocumentProductLinePrestationStatusInput>
> {
  constructor(props?: Partial<DocumentProductLinePrestationStatusInput>) {
    const defaultProps: Partial<DocumentProductLinePrestationStatusInput> = {
      name: faker.company.name(),
      key: faker.git.branch(),
    };
    super(DocumentProductLinePrestationStatus, defaultProps, props);
  }
}

export default function DocumentProductLinePrestationStatusFactory(
  input?: Partial<DocumentProductLinePrestationStatusInput>,
): DocumentProductLinePrestationStatusFact {
  return new DocumentProductLinePrestationStatusFact(input);
}
