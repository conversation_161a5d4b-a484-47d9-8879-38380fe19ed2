import { DocumentProductLinePrestationSubOption, DocumentProductLinePrestationSubOptionInput } from '../../models';
import { faker } from '@faker-js/faker';
import BaseFactory from './base-factory';

class DocumentProductLinePrestationSubOptionFact extends BaseFactory<
  DocumentProductLinePrestationSubOption,
  Partial<DocumentProductLinePrestationSubOptionInput>
> {
  constructor(props?: Partial<DocumentProductLinePrestationSubOptionInput>) {
    const defaultProps = {
      documentProductLinePrestationId: faker.number.int({ min: 1, max: 9 }),
      subOptionId: faker.number.int({ min: 1, max: 9 }),
      optionLabel: faker.company.name(),
      subOptionLabel: faker.word.words(),
    };
    super(DocumentProductLinePrestationSubOption, defaultProps, props);
  }
}
export default function DocumentProductLinePrestationSubOptionFactory(
  input?: Partial<DocumentProductLinePrestationSubOptionInput>,
): DocumentProductLinePrestationSubOptionFact {
  return new DocumentProductLinePrestationSubOptionFact(input);
}
