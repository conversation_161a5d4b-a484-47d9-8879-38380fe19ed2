import {
  DocumentProductLinePrestation,
  DocumentProductLinePrestationInput,
} from '../../models/DocumentProductLinePrestation';
import { faker } from '@faker-js/faker';
import BaseFactory from './base-factory';

class DocumentProductLinePrestationFact extends BaseFactory<
  DocumentProductLinePrestation,
  Partial<DocumentProductLinePrestationInput>
> {
  constructor(props?: Partial<DocumentProductLinePrestationInput>) {
    const defaultProps = {
      prestationDate: faker.date.recent().toISOString(),
      booksPackageId: faker.string.sample({ min: 10, max: 10 }),
      booksShipmentId: faker.string.sample({ min: 10, max: 10 }),
      boOrderId: faker.string.sample({ min: 10, max: 10 }),
      isActive: faker.datatype.boolean(),
      documentProductLineId: faker.number.int({ min: 1, max: 9 }),
      documentProductLinePrestationStatusId: faker.number.int({ min: 1, max: 9 }),
    };
    super(DocumentProductLinePrestation, defaultProps, props);
  }
}
export default function DocumentProductLinePrestationFactory(
  input?: Partial<DocumentProductLinePrestationInput>,
): DocumentProductLinePrestationFact {
  return new DocumentProductLinePrestationFact(input);
}
