import { faker } from '@faker-js/faker';
import BaseFactory from './base-factory';
import { DocumentProductLinePrice, DocumentProductLinePriceInput } from '../../models';

class DocumentProductLinePriceFact extends BaseFactory<
  DocumentProductLinePrice,
  Partial<DocumentProductLinePriceInput>
> {
  constructor(props?: Partial<DocumentProductLinePriceInput>) {
    const defaultProps = {
      priceId: faker.number.int({ min: 1, max: 9 }),
      priceOptionId: faker.number.int({ min: 1, max: 9 }),
      value: faker.number.int({ min: 1000, max: 9999 }),
      priceLabel: faker.company.name(),
      priceOptionLabel: faker.company.name(),
    };
    super(DocumentProductLinePrice, defaultProps, props);
  }
}
export default function DocumentProductLinePriceFactory(
  input?: Partial<DocumentProductLinePriceInput>,
): DocumentProductLinePriceFact {
  return new DocumentProductLinePriceFact(input);
}
