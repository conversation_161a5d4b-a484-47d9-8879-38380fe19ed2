import { DocumentProductLineSubOption, DocumentProductLineSubOptionInput } from '../../models';
import { faker } from '@faker-js/faker';
import BaseFactory from './base-factory';

class DocumentProductLineSubOptionFact extends BaseFactory<
  DocumentProductLineSubOption,
  Partial<DocumentProductLineSubOptionInput>
> {
  constructor(props?: Partial<DocumentProductLineSubOptionInput>) {
    const defaultProps = {
      documentProductLineId: faker.number.int({ min: 1, max: 9 }),
      subOptionId: faker.number.int({ min: 1, max: 9 }),
      optionLabel: faker.company.name(),
      subOptionLabel: faker.word.words(),
    };
    super(DocumentProductLineSubOption, defaultProps, props);
  }
}
export default function DocumentProductLineSubOptionFactory(
  input?: Partial<DocumentProductLineSubOptionInput>,
): DocumentProductLineSubOptionFact {
  return new DocumentProductLineSubOptionFact(input);
}
