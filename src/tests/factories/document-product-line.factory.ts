import { DocumentProductLine, DocumentProductLineInput } from './../../models/DocumentProductLine';
import { faker } from '@faker-js/faker';
import BaseFactory from './base-factory';

class DocumentProductLineFact extends BaseFactory<DocumentProductLine, Partial<DocumentProductLineInput>> {
  constructor(props?: Partial<DocumentProductLineInput>) {
    const defaultProps = {
      productId: faker.number.int({ min: 1, max: 9 }),
      productNameForClient: faker.company.name(),
      description: faker.finance.transactionDescription(),
      priceFamilyId: faker.number.int({ min: 1, max: 9 }),
      quantity: faker.number.float({ min: 1, max: 9 }),
      totalBeforeDiscount: faker.number.int({ min: 1000, max: 9999 }),
      discount: faker.number.int({ min: 1, max: 50 }),
      total: faker.number.int({ min: 9000, max: 9999 }),
      isSetTotalZero: faker.datatype.boolean(),
      booksProductLineId: faker.number.int({ min: 1, max: 9 }),
      productTypeUnitId: faker.number.int({ min: 1, max: 9 }),
      productTypeUnitLabel: faker.company.name(),
    };
    super(DocumentProductLine, defaultProps, props);
  }
}
export default function DocumentProductLineFactory(input?: Partial<DocumentProductLineInput>): DocumentProductLineFact {
  return new DocumentProductLineFact(input);
}
