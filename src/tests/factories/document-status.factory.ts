import { faker } from '@faker-js/faker';
import BaseFactory from './base-factory';
import { DocumentStatus, DocumentStatusInput } from '../../models';

/**
 * DocumentStatus Factory
 */
class DocumentStatusFact extends BaseFactory<DocumentStatus, Partial<DocumentStatusInput>> {
  constructor(props?: Partial<DocumentStatusInput>) {
    const defaultProps: Partial<DocumentStatusInput> = {
      name: faker.company.name(),
      key: faker.company.name(),
      status: faker.company.name(),
      zohoStatus: faker.company.name(),
      zohoCurrentSubStatus: faker.company.name(),
    };
    super(DocumentStatus, defaultProps, props);
  }
}

export default function DocumentStatusFactory(input?: Partial<DocumentStatusInput>): DocumentStatusFact {
  return new DocumentStatusFact(input);
}
