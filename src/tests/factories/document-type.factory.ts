import { faker } from '@faker-js/faker';
import BaseFactory from './base-factory';
import { DocumentType, DocumentTypeInput } from '../../models';

class DocumentTypeFact extends BaseFactory<DocumentType, Partial<DocumentTypeInput>> {
  constructor(props?: Partial<DocumentTypeInput>) {
    const defaultProps: Partial<DocumentTypeInput> = {
      name: faker.company.name(),
      key: faker.git.branch(),
    };
    super(DocumentType, defaultProps, props);
  }
}

export default function DocumentTypeFactory(input?: Partial<DocumentTypeInput>): DocumentTypeFact {
  return new DocumentTypeFact(input);
}
