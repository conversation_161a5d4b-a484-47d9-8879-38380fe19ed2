import { faker } from '@faker-js/faker';
import BaseFactory from './base-factory';
import { Document, DocumentInput } from '../../models';

class DocumentFact extends BaseFactory<Document, Partial<DocumentInput>> {
  constructor(props?: Partial<DocumentInput>) {
    const defaultProps: Partial<DocumentInput> = {
      documentTypeId: faker.number.int({ min: 1, max: 3 }),
      cdeZoho: faker.string.sample({ min: 10, max: 10 }),
      booksDocumentId: faker.string.sample({ min: 10, max: 10 }),
      vendeur: faker.number.int({ min: 100, max: 999 }),
      referent: faker.number.int({ min: 100, max: 999 }),
      contactId: faker.number.int({ min: 1, max: 8 }),
      contactPersonId: faker.number.int({ min: 100, max: 999 }),
      bdcClient: faker.string.sample({ min: 10, max: 10 }),
      contactSurPlace: faker.string.sample({ min: 10, max: 10 }),
      billingAddressId: faker.number.int({ min: 100, max: 999 }),
      billingAddressFull: faker.string.sample({ min: 10, max: 10 }),
      siteAddressId: faker.number.int({ min: 100, max: 999 }),
      siteAddressFull: faker.string.sample({ min: 10, max: 10 }),
      siteAddressIsGps: faker.datatype.boolean(),
      siteAddressLatitude: faker.location.latitude({ precision: 8 }).toString(),
      siteAddressLongitude: faker.location.longitude({ precision: 8 }).toString(),
      objectDuDocument: faker.string.sample({ min: 10, max: 10 }),
      demandeCommerciale: faker.string.sample({ min: 10, max: 10 }),
      responseLogistique: faker.string.sample({ min: 10, max: 10 }),
      prestationVueClient: faker.string.sample({ min: 10, max: 10 }),
      priceDescription: faker.string.sample({ min: 10, max: 10 }),
      paymentStatus: faker.string.sample({ min: 10, max: 10 }),
      paymentType: faker.string.sample({ min: 10, max: 10 }),
      paiement: faker.string.sample({ min: 10, max: 10 }),
      montantPaiement: faker.string.sample({ min: 10, max: 10 }),
      conditionsDePaiement: faker.string.sample({ min: 10, max: 10 }),
      retainerInvoiceSubTotal: faker.string.sample({ min: 10, max: 10 }),
      retainerinvoiceId: faker.string.sample({ min: 10, max: 10 }),
      logComment: faker.string.sample({ min: 10, max: 10 }),
      isActive: faker.datatype.boolean(),
      documentStatusId: faker.number.int({ min: 1, max: 4 }),
    };

    super(Document, defaultProps, props);
  }
}

export default function DocumentFactory(input?: Partial<DocumentInput>): DocumentFact {
  return new DocumentFact(input);
}
