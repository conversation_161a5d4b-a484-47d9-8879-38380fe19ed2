import { faker } from '@faker-js/faker';
import BaseFactory from './base-factory';
import { DocumentFileUploadInput, DocumentFileUpload } from '../../models';

class DocumentFileUploadFact extends BaseFactory<DocumentFileUpload, Partial<DocumentFileUploadInput>> {
  constructor(props?: Partial<DocumentFileUploadInput>) {
    const defaultProps = {
      name: faker.company.name(),
      url: faker.internet.url(),
      type: faker.helpers.arrayElement(['BDC_CLIENT', 'PAYMENT']),
    };
    super(DocumentFileUpload, defaultProps, props);
  }
}

export default function DocumentFileUploadFactory(input?: Partial<DocumentFileUploadInput>) {
  return new DocumentFileUploadFact(input);
}
