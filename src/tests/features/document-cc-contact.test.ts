import { APIGatewayProxyResult, Context } from 'aws-lambda';
import { describe, it, expect, afterAll } from '@jest/globals';
import { sequelize } from 'database-layer';
import { handler } from '../../handlers/document-cc-contact-handler';
import { generateApiGatewayEvent } from '../mock-events';
import { Document, DocumentCCContact } from '../../models';
import { DocumentCCContactFactory, DocumentFactory } from '../factories';

describe('Document CC Contact API Handler', function () {
  let document: Document;
  const documentCCContactInput = DocumentCCContactFactory().make();
  let createdDocumentCCContactId: number;

  beforeAll(async () => {
    document = await DocumentFactory().create();
    documentCCContactInput.documentId = document.id;
  });

  afterAll(async () => {
    await sequelize.close();
  });

  it('create Document CC Contact should return 400', async () => {
    const event = generateApiGatewayEvent({
      path: '/document-cc-contacts',
      httpMethod: 'POST',
      body: {},
    });
    const context: Context = {} as Context;
    const result: APIGatewayProxyResult = await handler(event, context);
    expect(result.statusCode).toEqual(400);
    const body = JSON.parse(result.body);
    expect(body.validationErrors).toMatchObject([
      { params: { missingProperty: 'documentId' } },
      { params: { missingProperty: 'contactPersonId' } },
    ]);
  });

  it('create Document CC Contact should create a Document CC Contact', async () => {
    const event = generateApiGatewayEvent({
      path: '/document-cc-contacts',
      httpMethod: 'POST',
      body: documentCCContactInput,
    });
    const context: Context = {} as Context;
    const result: APIGatewayProxyResult = await handler(event, context);
    expect(result.statusCode).toEqual(200);
    const body = JSON.parse(result.body);
    createdDocumentCCContactId = body.id;
    expect(body).toMatchObject(documentCCContactInput);
  });

  it('update Document CC Contact should return 400', async () => {
    const event = generateApiGatewayEvent({
      path: `/document-cc-contacts/${createdDocumentCCContactId}`,
      httpMethod: 'PUT',
      body: {},
    });
    const context: Context = {} as Context;
    const result: APIGatewayProxyResult = await handler(event, context);
    expect(result.statusCode).toEqual(400);
    const body = JSON.parse(result.body);
    expect(body.validationErrors).toMatchObject([
      { params: { missingProperty: 'documentId' } },
      { params: { missingProperty: 'contactPersonId' } },
    ]);
  });

  it('update Document CC Contact should update a Document CC Contact', async () => {
    documentCCContactInput.contactPersonId = 2;
    const event = generateApiGatewayEvent({
      path: `/document-cc-contacts/${createdDocumentCCContactId}`,
      httpMethod: 'PUT',
      body: documentCCContactInput,
    });
    const context: Context = {} as Context;
    const result: APIGatewayProxyResult = await handler(event, context);
    expect(result.statusCode).toEqual(200);
    const body = JSON.parse(result.body);
    expect(body).toMatchObject(documentCCContactInput);
  });

  it('getDocumentCCContacts should fetch a list of Document CC Contacts', async () => {
    const event = generateApiGatewayEvent({
      path: '/document-cc-contacts',
      httpMethod: 'GET',
    });
    const context: Context = {} as Context;
    const result: APIGatewayProxyResult = await handler(event, context);
    expect(result.statusCode).toEqual(200);
    const body = JSON.parse(result.body) as { rows: DocumentCCContact[]; count: number };
    expect(Array.isArray(body.rows)).toBe(true);
    expect(body.count).toBeGreaterThan(0);
    body.rows.forEach((item: DocumentCCContact) => {
      expect(item).toEqual(
        expect.objectContaining({
          id: expect.any(Number),
          documentId: expect.any(Number),
          contactPersonId: expect.any(Number),
          createdAt: expect.any(String),
          updatedAt: expect.any(String),
          deletedAt: null,
        }),
      );
    });
  });

  it('getDocumentCCContactById should get a single Document Status', async () => {
    const event = generateApiGatewayEvent({
      path: `/document-cc-contacts/${createdDocumentCCContactId}`,
      httpMethod: 'GET',
    });
    const context: Context = {} as Context;
    const result: APIGatewayProxyResult = await handler(event, context);
    expect(result.statusCode).toEqual(200);
    const body = JSON.parse(result.body) as DocumentCCContact;
    expect(body).toEqual(
      expect.objectContaining({
        id: createdDocumentCCContactId,
        documentId: document.id,
        contactPersonId: documentCCContactInput.contactPersonId,
        createdAt: expect.any(String),
        updatedAt: expect.any(String),
        deletedAt: null,
      }),
    );
  });

  it('deleteDocumentCCContact should delete a Document CC Contact', async () => {
    const event = generateApiGatewayEvent({
      path: `/document-cc-contacts/${createdDocumentCCContactId}`,
      httpMethod: 'DELETE',
    });
    const context: Context = {} as Context;
    const result: APIGatewayProxyResult = await handler(event, context);
    expect(result.statusCode).toEqual(200);
  });
});
