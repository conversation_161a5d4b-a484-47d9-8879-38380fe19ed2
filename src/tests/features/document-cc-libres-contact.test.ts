import { BaseUnitTest } from '../base-unit-test';
import DocumentCCLibresContactFactory from '../factories/document-cc-libres-contact.factory';
import { APIGatewayProxyResult, Context } from 'aws-lambda';
import { handler } from '../../handlers/document-cc-libres-contact-handler';
import DocumentFactory from '../factories/document.factory';

class DocumentCCLibresContactServiceTest extends BaseUnitTest {
  protected serviceName = 'document-cc-libres-contacts';
  protected async handler(event: any, context: Context): Promise<APIGatewayProxyResult> {
    return handler(event, context);
  }

  constructor(testData: { newData: any; updateData: any; entityId: number }) {
    super(testData);
  }

  protected async createTestData(): Promise<void> {
    // Logic to create test data for DocumentCCLibresContactService
    const documentFactory = await DocumentFactory().create();
    const documentCCLibresContact = await DocumentCCLibresContactFactory({
      documentId: documentFactory.id,
    }).create();
    this.testData.entityId = documentCCLibresContact.id;
    this.testData.newData = DocumentCCLibresContactFactory({
      documentId: documentFactory.id,
    }).make();
    this.testData.updateData = DocumentCCLibresContactFactory({
      documentId: documentFactory.id,
    }).make();
  }

  protected async clearTestData(): Promise<void> {}
}

// Example test data
const testData = {
  newData: {},
  updateData: {},
  entityId: 1,
};

// Instantiate the test class and run tests
const documentCCLibresContactServiceTest = new DocumentCCLibresContactServiceTest(testData);
documentCCLibresContactServiceTest.describeTests({
  //runListTest: false,
  customPaths: {
    listPath: '/document-cc-libres-contacts',
  },
});
