import { handler } from '../../handlers/document-product-line-prestation-statuses-handler';
import { BaseUnitTest } from '../base-unit-test';
import { APIGatewayProxyResult, Context } from 'aws-lambda';
import DocumentProductLinePrestationStatusFactory from '../factories/document-product-line-prestation-status.factory';
class DocumentProductLinePrestationStatusServiceTest extends BaseUnitTest {
  protected serviceName = 'document-product-line-prestation-statuses';
  protected async handler(event: any, context: Context): Promise<APIGatewayProxyResult> {
    return handler(event, context);
  }

  constructor(testData: { newData: any; updateData: any; entityId: number }) {
    super(testData);
  }

  protected async createTestData(): Promise<void> {
    // Logic to create test data for DocumentProductLinePrestationStatusService
    const documentProductLinePrestationStatus = await DocumentProductLinePrestationStatusFactory().create();
    this.testData.entityId = documentProductLinePrestationStatus.id;
    this.testData.newData = DocumentProductLinePrestationStatusFactory().make();
    this.testData.updateData = DocumentProductLinePrestationStatusFactory().make();
  }

  protected async clearTestData(): Promise<void> {
    // Logic to clear test data for DocumentProductLineService
  }
}
// Example test data
const testData = {
  newData: {},
  updateData: {},
  entityId: 1,
};
const documentProductLinePrestationStatusServiceTest = new DocumentProductLinePrestationStatusServiceTest(testData);
documentProductLinePrestationStatusServiceTest.describeTests();
