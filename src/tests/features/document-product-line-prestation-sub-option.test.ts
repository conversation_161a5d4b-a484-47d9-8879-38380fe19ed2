import { handler } from '../../handlers/document-product-line-prestation-sub-option-handler';
import { BaseUnitTest } from '../base-unit-test';
import { APIGatewayProxyResult, Context } from 'aws-lambda';
import DocumentProductLinePrestationFactory from '../factories/document-product-line-prestation.factory';
import DocumentProductLinePrestationSubOptionFactory from '../factories/document-product-line-prestation-sub-option.factory';
import DocumentProductLinePrestationStatusFactory from '../factories/document-product-line-prestation-status.factory';
import { DocumentFactory, DocumentTypeFactory } from '../factories';
import DocumentProductLineFactory from '../factories/document-product-line.factory';

class DocumentProductLinePrestationSubOptionServiceTest extends BaseUnitTest {
  protected serviceName = 'document-product-line-prestation-sub-options';
  protected async handler(event: any, context: Context): Promise<APIGatewayProxyResult> {
    return handler(event, context);
  }

  constructor(testData: { newData: any; updateData: any; entityId: number }) {
    super(testData);
  }

  protected async createTestData(): Promise<void> {
    // Logic to create test data for DocumentProductLineSubOptionService

    const documentTypeFactory = await DocumentTypeFactory().create();
    const documentFactory = await DocumentFactory({ documentTypeId: documentTypeFactory.id }).create();
    const documentProductLineFactory = await DocumentProductLineFactory({ documentId: documentFactory.id }).create();
    const documentProductLinePrestationStatusFactory = await DocumentProductLinePrestationStatusFactory().create();
    const documentProductLinePrestationFactory = await DocumentProductLinePrestationFactory({
      documentProductLineId: documentProductLineFactory.id,
      documentProductLinePrestationStatusId: documentProductLinePrestationStatusFactory.id,
    }).create();
    const documentProductLinePrestationSubOption = await DocumentProductLinePrestationSubOptionFactory({
      documentProductLinePrestationId: documentProductLinePrestationFactory.id,
    }).create();
    this.testData.entityId = documentProductLinePrestationSubOption.id;
    this.testData.newData = DocumentProductLinePrestationSubOptionFactory({
      documentProductLinePrestationId: documentProductLinePrestationSubOption.id,
    }).make();
    this.testData.updateData = DocumentProductLinePrestationSubOptionFactory({
      documentProductLinePrestationId: documentProductLinePrestationSubOption.id,
    }).make();
  }

  protected async clearTestData(): Promise<void> {
    // Logic to clear test data for DocumentProductLineService
  }
}
// Example test data
const testData = {
  newData: {},
  updateData: {},
  entityId: 1,
};
const documentProductLineSubOptionServiceTest = new DocumentProductLinePrestationSubOptionServiceTest(testData);
documentProductLineSubOptionServiceTest.describeTests();
