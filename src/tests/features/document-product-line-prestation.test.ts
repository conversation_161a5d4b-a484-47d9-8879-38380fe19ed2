import { BaseUnitTest } from '../base-unit-test';
import { APIGatewayProxyResult, Context } from 'aws-lambda';
import DocumentProductLinePrestationFactory from '../factories/document-product-line-prestation.factory';
import DocumentProductLineFactory from '../factories/document-product-line.factory';
import DocumentProductLinePrestationStatusFactory from '../factories/document-product-line-prestation-status.factory';
import { handler } from '../../handlers/document-product-line-prestation-handler';
import { DocumentFactory, DocumentTypeFactory } from '../factories';

class DocumentProductLinePrestationServiceTest extends BaseUnitTest {
  protected serviceName = 'document-product-line-prestations';
  protected async handler(event: any, context: Context): Promise<APIGatewayProxyResult> {
    return handler(event, context);
  }

  constructor(testData: { newData: any; updateData: any; entityId: number }) {
    super(testData);
  }

  protected async createTestData(): Promise<void> {
    // Logic to create test data for DocumentProductLinePrestationService
    const documentTypeFactory = await DocumentTypeFactory().create();
    const documentFactory = await DocumentFactory({ documentTypeId: documentTypeFactory.id }).create();
    const documentProductLineFactory = await DocumentProductLineFactory({ documentId: documentFactory.id }).create();
    const documentProductLinePrestationStatusFactory = await DocumentProductLinePrestationStatusFactory().create();
    const documentProductLinePrestation = await DocumentProductLinePrestationFactory({
      documentProductLineId: documentProductLineFactory.id,
      documentProductLinePrestationStatusId: documentProductLinePrestationStatusFactory.id,
    }).create();
    this.testData.entityId = documentProductLinePrestation.id;
    this.testData.newData = DocumentProductLinePrestationFactory({
      documentProductLineId: documentProductLineFactory.id,
      documentProductLinePrestationStatusId: documentProductLinePrestationStatusFactory.id,
    }).make();
    this.testData.updateData = DocumentProductLinePrestationFactory({
      documentProductLineId: documentProductLineFactory.id,
      documentProductLinePrestationStatusId: documentProductLinePrestationStatusFactory.id,
    }).make();
  }

  protected async clearTestData(): Promise<void> {
    // Logic to clear test data for DocumentProductLinePrestationService
  }
}
// Example test data
const testData = {
  newData: {},
  updateData: {},
  entityId: 1,
};
const documentProductLinePrestationServiceTest = new DocumentProductLinePrestationServiceTest(testData);
documentProductLinePrestationServiceTest.describeTests();
