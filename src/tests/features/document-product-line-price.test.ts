import { handler } from '../../handlers/document-product-line-price-handler';
import { BaseUnitTest } from '../base-unit-test';
import { APIGatewayProxyResult, Context } from 'aws-lambda';
import { DocumentFactory } from '../factories';
import DocumentProductLineFactory from '../factories/document-product-line.factory';
import DocumentProductLinePriceFactory from '../factories/document-product-line-price.factory';

class DocumentProductLinePriceServiceTest extends BaseUnitTest {
  protected serviceName = 'document-product-line-price';

  protected async handler(event: any, context: Context): Promise<APIGatewayProxyResult> {
    return handler(event, context);
  }

  constructor(testData: { newData: any; updateData: any; entityId: number }) {
    super(testData);
  }

  protected async createTestData(): Promise<void> {
    // Logic to create test data for DocumentProductLinePriceService
    const documentFactory = await DocumentFactory().create();
    const documentProductLine = await DocumentProductLineFactory({
      documentId: documentFactory.id,
    }).create();
    const documentProductLinePrice = await DocumentProductLinePriceFactory({
      documentProductLineId: documentProductLine.id,
    }).create();
    this.testData.entityId = documentProductLinePrice.id;
    this.testData.newData = DocumentProductLinePriceFactory({
      documentProductLineId: documentProductLine.id,
    }).make();
    this.testData.updateData = DocumentProductLinePriceFactory({
      documentProductLineId: documentProductLine.id,
    }).make();
  }
  protected async clearTestData(): Promise<void> {
    // Logic to clear test data for DocumentProductLineService
  }
}
const testData = {
  newData: {},
  updateData: {},
  entityId: 1,
};
const documentProductLinePriceServiceTest = new DocumentProductLinePriceServiceTest(testData);
documentProductLinePriceServiceTest.describeTests();
