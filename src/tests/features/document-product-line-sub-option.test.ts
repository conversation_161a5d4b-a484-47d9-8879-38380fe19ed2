import { handler } from '../../handlers/document-product-line-sub-option-handler';
import { BaseUnitTest } from '../base-unit-test';
import { APIGatewayProxyResult, Context } from 'aws-lambda';
import DocumentProductLineSubOptionFactory from '../factories/document-product-line-sub-option.factory';
import DocumentProductLineFactory from '../factories/document-product-line.factory';
import { DocumentFactory } from '../factories';

class DocumentProductLineSubOptionServiceTest extends BaseUnitTest {
  protected serviceName = 'document-product-line-sub-options';
  protected async handler(event: any, context: Context): Promise<APIGatewayProxyResult> {
    return handler(event, context);
  }

  constructor(testData: { newData: any; updateData: any; entityId: number }) {
    super(testData);
  }

  protected async createTestData(): Promise<void> {
    // Logic to create test data for DocumentProductLineSubOptionService
    const documentFactory = await DocumentFactory().create();
    const documentProductLineFactory = await DocumentProductLineFactory({
      documentId: documentFactory.id,
    }).create();
    const documentProductLineSubOption = await DocumentProductLineSubOptionFactory({
      documentProductLineId: documentProductLineFactory.id,
    }).create();
    this.testData.entityId = documentProductLineSubOption.id;
    this.testData.newData = DocumentProductLineSubOptionFactory({
      documentProductLineId: documentProductLineFactory.id,
    }).make();
    this.testData.updateData = DocumentProductLineSubOptionFactory({
      documentProductLineId: documentProductLineFactory.id,
    }).make();
  }

  protected async clearTestData(): Promise<void> {
    // Logic to clear test data for DocumentProductLineService
  }
}
// Example test data
const testData = {
  newData: {},
  updateData: {},
  entityId: 1,
};
const documentProductLineSubOptionServiceTest = new DocumentProductLineSubOptionServiceTest(testData);
documentProductLineSubOptionServiceTest.describeTests();
