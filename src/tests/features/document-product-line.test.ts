import { handler } from '../../handlers/document-product-lines-handler';
import { BaseUnitTest } from '../base-unit-test';
import { APIGatewayProxyResult, Context } from 'aws-lambda';
import { DocumentFactory } from '../factories';
import DocumentProductLineFactory from '../factories/document-product-line.factory';

class DocumentProductLineServiceTest extends BaseUnitTest {
  protected serviceName = 'document-product-line';
  protected async handler(event: any, context: Context): Promise<APIGatewayProxyResult> {
    return handler(event, context);
  }

  constructor(testData: { newData: any; updateData: any; entityId: number }) {
    super(testData);
  }

  protected async createTestData(): Promise<void> {
    // Logic to create test data for DocumentProductLineService
    const documentFactory = await DocumentFactory().create();
    const documentProductLine = await DocumentProductLineFactory({
      documentId: documentFactory.id,
    }).create();
    this.testData.entityId = documentProductLine.id;
    this.testData.newData = DocumentProductLineFactory({
      documentId: documentFactory.id,
    }).make();
    this.testData.updateData = DocumentProductLineFactory({
      documentId: documentFactory.id,
    }).make();
  }

  protected async clearTestData(): Promise<void> {
    // Logic to clear test data for DocumentProductLineService
  }
}
// Example test data
const testData = {
  newData: {},
  updateData: {},
  entityId: 1,
};
const documentProductLineServiceTest = new DocumentProductLineServiceTest(testData);
documentProductLineServiceTest.describeTests();
