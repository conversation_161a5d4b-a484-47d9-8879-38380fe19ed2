import { APIGatewayProxyResult, Context } from 'aws-lambda';
import { describe, it, expect, afterAll } from '@jest/globals';
import { sequelize } from 'database-layer';
import { handler } from '../../handlers/document-status-handler';
import { generateApiGatewayEvent } from '../mock-events';
import { DocumentStatus } from '../../models';

describe('Document Status API Handler', function () {
  afterAll(async () => {
    sequelize.close();
  });

  it('getDocumentStatuses should fetch a list of Document Statuses', async () => {
    const event = generateApiGatewayEvent({
      path: '/document-statuses',
      httpMethod: 'GET',
    });
    const context: Context = {} as Context;
    const result: APIGatewayProxyResult = await handler(event, context);
    expect(result.statusCode).toEqual(200);
    const body = JSON.parse(result.body) as DocumentStatus[];
    expect(Array.isArray(body)).toBe(true);
    body.forEach((item: DocumentStatus) => {
      expect(item).toEqual(
        expect.objectContaining({
          id: expect.any(Number),
          name: expect.any(String),
          createdAt: expect.any(String),
          updatedAt: expect.any(String),
          deletedAt: null,
        }),
      );
    });
  });

  it('getDocumentStatusById should get a single Document Status', async () => {
    const event = generateApiGatewayEvent({
      path: '/document-statuses/1',
      httpMethod: 'GET',
    });
    const context: Context = {} as Context;
    const result: APIGatewayProxyResult = await handler(event, context);
    expect(result.statusCode).toEqual(200);
    const body = JSON.parse(result.body) as DocumentStatus[];
    expect(body).toEqual(
      expect.objectContaining({
        id: expect.any(Number),
        name: expect.any(String),
        createdAt: expect.any(String),
        updatedAt: expect.any(String),
        deletedAt: null,
      }),
    );
  });
});
