import { APIGatewayProxyResult, Context } from 'aws-lambda';
import { describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import { sequelize, SuccessResponse } from 'database-layer';
import { DocumentType } from '../../models';
import { DocumentTypeFactory } from '../factories';
import { generateApiGatewayEvent } from '../mock-events';
import { handler } from '../../handlers/document-type-handler';

describe('DocumentType API Handler', function () {
  const context: Context = {} as Context;

  afterAll(async () => {
    // Closing the DB connection allows Je<PERSON> to exit successfully.
    sequelize.close();
  });

  beforeAll(async () => {
    await DocumentTypeFactory().create();
    await DocumentTypeFactory().create();
    await DocumentTypeFactory().create();
  });

  it('getDocumentTypes should fetch a DocumentType list', async () => {
    const event = generateApiGatewayEvent({
      path: '/document-types',
      httpMethod: 'GET',
    });
    const result: APIGatewayProxyResult = await handler(event, context);
    expect(result.statusCode).toEqual(200);
    const body = JSON.parse(result.body) as {
      count: number;
      rows: DocumentType[];
    };

    // Assert that the result is an instance of SuccessResponse
    expect(result).toBeInstanceOf(SuccessResponse);
    if (body.count <= 0) {
      // If the body is empty, expect it to be an empty array
      expect(body.rows).toEqual([]);
    } else {
      // If the body is not empty, expect it to be an array of DocumentType objects
      expect(Array.isArray(body.rows)).toBe(true);
      body.rows.forEach((documentType) => {
        expect(documentType).toEqual(
          expect.objectContaining({
            id: expect.any(Number),
            name: documentType.name === null ? null : expect.any(String),
            key: documentType.key === null ? null : expect.any(String),
            createdAt: documentType.createdAt === null ? null : expect.any(String),
            updatedAt: documentType.updatedAt === null ? null : expect.any(String),
            deletedAt: documentType.deletedAt === null ? null : expect.any(String),
          }),
        );
      });
    }
  });
});
