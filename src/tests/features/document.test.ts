import { BaseUnitTest } from '../base-unit-test';
import DocumentFactory from '../factories/document.factory';
import { APIGatewayProxyResult, Context } from 'aws-lambda';
import { handler } from '../../handlers/document-handler';
import DocumentStatusFactory from '../factories/document-status.factory';
import { DocumentCCContactFactory, DocumentTypeFactory } from '../factories';
import DocumentProductLineFactory from '../factories/document-product-line.factory';
import DocumentProductLinePriceFactory from '../factories/document-product-line-price.factory';
import DocumentProductLineSubOptionFactory from '../factories/document-product-line-sub-option.factory';
import DocumentProductLinePrestationFactory from '../factories/document-product-line-prestation.factory';
import DocumentProductLinePrestationStatusFactory from '../factories/document-product-line-prestation-status.factory';
import { describe, it, expect, beforeAll } from '@jest/globals';
import { generateApiGatewayEvent } from '../mock-events';
import DocumentService, { DocumentDataInput } from '../../services/document.service';
import {
  DocumentCCContactInput,
  DocumentInput,
  DocumentProductLineInput,
  DocumentProductLinePrestationInput,
  DocumentProductLinePrestationStatusInput,
  DocumentProductLinePriceInput,
  DocumentProductLineSubOptionInput,
  DocumentStatusInput,
  DocumentTypeInput,
} from '../../models';
import ContactFactory from '../factories/contact.factory';

const documentService = new DocumentService();
describe('Document API Handler', function () {
  let documentProductLine: DocumentProductLineInput;
  let documentProductLinePrices: DocumentProductLinePriceInput;
  let documentProductLineSubOption: DocumentProductLineSubOptionInput;
  let documentProductLinePrestationStatus: DocumentProductLinePrestationStatusInput;
  let documentProductLinePrestation: DocumentProductLinePrestationInput;
  let documentStatus: DocumentStatusInput;
  let documentType: DocumentTypeInput;
  let document: DocumentInput;
  let document1: DocumentInput;
  let document2: DocumentInput;
  let documentCCContact: DocumentCCContactInput;
  let documentCCContact1: DocumentCCContactInput;
  let documentCCContact2: DocumentCCContactInput;

  beforeAll(async () => {
    documentProductLine = await DocumentProductLineFactory().make();
    documentProductLinePrices = await DocumentProductLinePriceFactory();
    documentProductLineSubOption = await DocumentProductLineSubOptionFactory().make();
    documentProductLinePrestationStatus = await DocumentProductLinePrestationStatusFactory().create();
    documentProductLinePrestation = await DocumentProductLinePrestationFactory({
      documentProductLinePrestationStatusId: documentProductLinePrestationStatus.id,
    }).make();
    documentStatus = await DocumentStatusFactory().create();
    documentType = await DocumentTypeFactory().create();
    document = await DocumentFactory({
      documentStatusId: documentStatus.id,
      documentTypeId: documentType.id,
    }).make();
    document1 = await DocumentFactory({
      documentStatusId: documentStatus.id,
      documentTypeId: documentType.id,
    }).create();
    document2 = await DocumentFactory({
      documentStatusId: documentStatus.id,
      documentTypeId: documentType.id,
    }).make();
    documentCCContact = await DocumentCCContactFactory({
      documentId: document1.id,
    }).create();
    documentCCContact1 = await DocumentCCContactFactory({
      documentId: document1.id,
    }).create();
    documentCCContact2 = await DocumentCCContactFactory({
      documentId: document1.id,
    }).create();
  });

  it('should create a new entity', async (): Promise<void> => {
    const input = {
      ...document,
      ccContactPersons: [documentCCContact.id, documentCCContact1.id, documentCCContact2.id],
      lineItems: [
        {
          ...documentProductLine,
          prestationDateLine: [{ ...documentProductLinePrestation }],
          productLinePrices: [{ ...documentProductLinePrices }],
          productLineSubOptions: [{ ...documentProductLineSubOption }],
        },
      ],
    } as DocumentDataInput; // Use external test data
    const result = await documentService.createOrUpdateDocument(input);
    console.log('result', result);
    expect(result).toEqual(expect.objectContaining({ ...document }));
  });

  it('should update an existing entity', async (): Promise<void> => {
    const input = {
      ...document2,
      documentId: document2.id,
      ccContactPersons: [documentCCContact.id, documentCCContact1.id, documentCCContact2.id],
      lineItems: [
        {
          ...documentProductLine,
          prestationDateLine: [{ ...documentProductLinePrestation }],
          productLinePrices: [{ ...documentProductLinePrices }],
          productLineSubOptions: [{ ...documentProductLineSubOption }],
        },
      ],
    } as DocumentDataInput & { documentId: number }; // Use external test data
    if (input) {
      const result = await documentService.createOrUpdateDocument(input);
      expect(result).toEqual(expect.objectContaining({ ...document2 }));
    }
  });
  it('should fetch update comment entity by id', async (): Promise<void> => {
    const context: Context = {} as Context;
    const requestBody = {
      comment: 'Comment update',
    };
    const event = generateApiGatewayEvent({
      path: `/document-update-comment/${document1.id}`,
      httpMethod: 'PUT',
      body: JSON.stringify(requestBody),
    });
    const result: APIGatewayProxyResult = await handler(event, context);
    expect(result.statusCode).toEqual(200);
  });
  it('should fetch a single entity by id', async (): Promise<void> => {
    const context: Context = {} as Context;
    const event = generateApiGatewayEvent({
      path: `/document/${document1.id}`,
      httpMethod: 'GET',
    });
    const result: APIGatewayProxyResult = await handler(event, context);
    expect(result.statusCode).toEqual(200);
  });

  it('should update status document Handle Prise en compte status', async (): Promise<void> => {
    const event = generateApiGatewayEvent({
      path: `/document/${document1.id}/status`,
      httpMethod: 'PUT',
      body: { document_status: 'considered' },
    });
    const context: Context = {} as Context;
    const result: APIGatewayProxyResult = await handler(event, context);
    expect(result.statusCode).toEqual(200);
  });

  it('should fetch update contact document', async (): Promise<void> => {
    const context: Context = {} as Context;
    const event = generateApiGatewayEvent({
      path: `/update-contact-document`,
      httpMethod: 'POST',
      body: { contactDestinationId: document1.contactId, contactOriginId: document2.contactId },
    });
    const result: APIGatewayProxyResult = await handler(event, context);
    expect(result.statusCode).toEqual(200);
  });
});

class DocumentServiceTest extends BaseUnitTest {
  protected serviceName = 'document';
  protected async handler(event: any, context: Context): Promise<APIGatewayProxyResult> {
    return handler(event, context);
  }

  constructor(testData: { newData: any; updateData: any; entityId: number }) {
    super(testData);
  }

  protected async createTestData(): Promise<void> {
    // Logic to create test data for DocumentService
    const documentStatusFactory = await DocumentStatusFactory().create();
    const contactFactory = await ContactFactory().create();
    const documentTypeFactory = await DocumentTypeFactory().create();
    const document = await DocumentFactory({
      documentStatusId: documentStatusFactory.id,
      documentTypeId: documentTypeFactory.id,
    }).create();
    const documentProductLine = await DocumentProductLineFactory().make();
    const documentProductLinePrices = await DocumentProductLinePriceFactory();
    const documentProductLineSubOption = await DocumentProductLineSubOptionFactory().make();
    const DocumentProductLinePrestationStatus = await DocumentProductLinePrestationStatusFactory().create();
    const documentProductLinePrestation = await DocumentProductLinePrestationFactory({
      documentProductLinePrestationStatusId: DocumentProductLinePrestationStatus.id,
    }).make();
    const documents = DocumentFactory({
      documentStatusId: documentStatusFactory.id,
      documentTypeId: documentTypeFactory.id,
      contactId: contactFactory.id,
    }).make();
    this.testData.entityId = document.id;
    this.testData.document = documents;
    this.testData.newData = {
      ...documents,
      lineItems: [
        {
          ...documentProductLine,
          prestationDateLine: [{ ...documentProductLinePrestation }],
          productLinePrices: [{ ...documentProductLinePrices }],
          productLineSubOptions: [{ ...documentProductLineSubOption }],
        },
      ],
    };
    this.testData.updateData = DocumentFactory({
      documentStatusId: documentStatusFactory.id,
      documentTypeId: documentTypeFactory.id,
    }).make();
  }

  protected async clearTestData(): Promise<void> {}

  public;
}

// Example test data
const testData = {
  newData: {},
  updateData: {},
  entityId: 1,
  document: {},
};

// Instantiate the test class and run tests
const documentServiceTest = new DocumentServiceTest(testData);
documentServiceTest.describeTests({
  //runListTest: false,
  customPaths: {
    listPath: '/documents',
  },
  runCreateTest: false,
  runUpdateTest: false,
  runGetByIdTest: false,
});
