import { APIGatewayProxyResult, Context } from 'aws-lambda';
import { BaseUnitTest } from '../base-unit-test';
import { handler } from '../../handlers/document-file-upload-handler';
import { DocumentFactory } from '../factories';
import DocumentFileUploadFactory from '../factories/document_file_upload.factory';

class DocumentFileUploadTest extends BaseUnitTest {
  protected serviceName = 'document-file-uploads';
  protected async handler(event: any, context: Context): Promise<APIGatewayProxyResult> {
    return handler(event, context);
  }

  constructor(testData: { newData: any; updateData: any; entityId: number }) {
    super(testData);
  }

  protected async createTestData(): Promise<void> {
    const document = await DocumentFactory().create();
    const documentFileUpload = await DocumentFileUploadFactory({
      documentId: document.id,
    }).create();
    this.testData.entityId = documentFileUpload.id;

    this.testData.newData = DocumentFileUploadFactory({
      documentId: document.id,
    }).make();
    this.testData.updateData = DocumentFileUploadFactory({
      documentId: document.id,
    }).make();
  }

  protected async clearTestData(): Promise<void> {}
}

const testData = {
  newData: {},
  updateData: {},
  entityId: 1,
};

const documentFileUploadTest = new DocumentFileUploadTest(testData);
documentFileUploadTest.describeTests({
  //runListTest: false,
  runCreateTest: false,
  runDeleteTest: false,
  customPaths: {
    listPath: '/document-file-uploads',
  },
});
