import { APIGatewayProxyEvent } from 'aws-lambda';
import { ProviderInvoiceService } from '../../services/provider-invoice.service';
import { describe, it, expect, beforeEach } from '@jest/globals';

describe('ProviderInvoiceService', () => {
  let service: ProviderInvoiceService;

  beforeEach(async () => {
    service = new ProviderInvoiceService();
  });

  describe('getInvoicesList', () => {
    it('should return empty list when no invoices exist', async () => {
      const event: Partial<APIGatewayProxyEvent> = {
        queryStringParameters: { page: '1', limit: '10' }
      };

      const result = await service.getInvoicesList(event as APIGatewayProxyEvent);
      
      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.data.rows).toEqual([]);
      expect(body.data.count).toBe(0);
    });
  });

  describe('getInvoiceCreationData', () => {
    it('should require prestataire parameter', async () => {
      const event: Partial<APIGatewayProxyEvent> = {
        queryStringParameters: {}
      };

      const result = await service.getInvoiceCreationData(event as APIGatewayProxyEvent);
      
      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.message).toContain('Prestataire parameter is required');
    });
  });

  describe('createInvoice', () => {
    it('should create a provider invoice successfully', async () => {
      const requestBody = {
        prestataire: 'EcoTrans S.A.',
        prestataireInvoiceNumber: 'FAC-2025-001',
        status: 'draft',
        totalAmount: 100.00,
        totalSelected: 100.00
      };

      const event: Partial<APIGatewayProxyEvent> = {
        body: JSON.stringify(requestBody)
      };

      const result = await service.createInvoice(event as APIGatewayProxyEvent);
      
      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.data.id).toBeDefined();
      expect(body.data.message).toContain('created successfully');
    });

    it('should require prestataire field', async () => {
      const requestBody = {
        totalAmount: 100.00
      };

      const event: Partial<APIGatewayProxyEvent> = {
        body: JSON.stringify(requestBody)
      };

      const result = await service.createInvoice(event as APIGatewayProxyEvent);
      
      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.message).toContain('Prestataire is required');
    });
  });
});
