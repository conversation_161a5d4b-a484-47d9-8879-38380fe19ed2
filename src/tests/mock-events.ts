import {
  APIGatewayEventRequestContextWithAuthorizer,
  APIGatewayProxyEventHeaders,
  APIGatewayProxyEventMultiValueHeaders,
  APIGatewayProxyEventMultiValueQueryStringParameters,
  APIGatewayProxyEventPathParameters,
  APIGatewayProxyEventQueryStringParameters,
  APIGatewayProxyEventStageVariables,
  SQSMessageAttribute,
} from 'aws-lambda';

export const generateApiGatewayEvent = ({
  httpMethod = 'GET',
  resource = '/',
  path = '/',
  headers = {},
  queryStringParameters = null,
  body = null,
  pathParameters = null,
}: {
  body?: any;
  headers?: APIGatewayProxyEventHeaders;
  multiValueHeaders?: APIGatewayProxyEventMultiValueHeaders;
  httpMethod?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  isBase64Encoded?: boolean;
  path?: string;
  pathParameters?: APIGatewayProxyEventPathParameters | null;
  queryStringParameters?: APIGatewayProxyEventQueryStringParameters | null;
  multiValueQueryStringParameters?: APIGatewayProxyEventMultiValueQueryStringParameters | null;
  stageVariables?: APIGatewayProxyEventStageVariables | null;
  requestContext?: APIGatewayEventRequestContextWithAuthorizer<any>;
  resource?: string;
}) => {
  return {
    httpMethod,
    resource,
    path,
    headers: { 'Content-Type': 'application/json', ...headers },
    queryStringParameters,
    pathParameters,
    body: JSON.stringify(body),
    requestContext: {
      requestId: 'abc123',
      resourceId: '123456',
      stage: 'test',
      identity: {
        sourceIp: '127.0.0.1',
      },
    },
  };
};

export const generateSQSEvent = ({
  body = '',
  messageAttributes = {},
}: {
  body: string;
  messageAttributes?: Record<string, SQSMessageAttribute>;
}) => {
  return {
    Records: [
      {
        messageId: '19dd0b57-b21e-4ac1-bd88-01bbb068cb78',
        receiptHandle: 'MessageReceiptHandle',
        body: body,
        attributes: {
          ApproximateReceiveCount: '1',
          SentTimestamp: Date.now().toString(),
          SenderId: '123456789012',
          ApproximateFirstReceiveTimestamp: Date.now().toString(),
        },
        messageAttributes: messageAttributes || {},
        md5OfBody: '{{{md5_of_body}}}',
        eventSource: 'aws:sqs',
        eventSourceARN: 'arn:aws:sqs:us-east-1:123456789012:MyQueue',
        awsRegion: 'us-east-1',
      },
    ],
  };
};
