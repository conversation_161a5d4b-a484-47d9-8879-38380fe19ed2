import {
  Document,
  DocumentFileUpload,
  DocumentProductLinePrestation,
  DocumentProductLinePrestationSubOption,
  DocumentProductLinePrice,
  DocumentProductLineSubOption,
} from '../models';
import { DocumentProductLine, DocumentProductLineInput } from '../models/DocumentProductLine';

export type ZohoAction = 'CREATE' | 'UPDATE' | 'DELETE' | 'UPDATE-FROM-ZOHO';

export type ZohoEventSource = 'document' | 'contact';

export type ZohoWebhookEvent = {
  action: ZohoAction;
  data: unknown;
  model: string;
};

export type ZohoWebhookDataEvent = {
  requestData: Record<string, unknown>;
  responseData: Record<string, unknown>;
};

export type ZohoResponseEvent = {
  action: 'CREATE' | 'UPDATE' | 'DELETE';
  data: object & { id: number | string };
  service: string;
  model: string;
};

export type ZohoRequestData = {
  action: string;
  model: string;
  service: string;
  data: {
    id: number;
    DocumentProductLines: (DocumentProductLine & { booksProductId?: string })[];
    submitType?: string;
    documentStatus?: string;
    DocumentFileUploads: DocumentFileUpload[];
    lineItems: (DocumentProductLineInput & {
      prestationDateLine?: DocumentProductLinePrestation | DocumentProductLinePrestationSubOption[];
      productLinePrices?: DocumentProductLinePrice[];
      productLineSubOptions?: DocumentProductLineSubOption[];
      priceFamilyValue?: string | number;
      name?: string;
      booksProductId?: string;
    })[];
  } & Document;
};

export type ZohoBodyResponseEvent = {
  requestData: ZohoRequestData;
  responseData: Record<string, unknown>;
};

export type ZohoEstimateAndOrderResponseEvent = {
  estimate: ZohoBodyResponseEvent;
  order: ZohoBodyResponseEvent;
};

export type ZohoEstimate = {
  estimate_id: string;
  estimate_number: string;
  zcrm_potential_id: string;
  zcrm_potential_name: string;
  date: string;
  date_formatted: string;
  created_date: string;
  created_date_formatted: string;
  reference_number: string;
  status: string;
  status_formatted: string;
  color_code: string;
  current_sub_status_id: string;
  current_sub_status: string;
  current_sub_status_formatted: string;
  customer_id: string;
  customer_name: string;
  is_signed_and_accepted: boolean;
  is_transaction_created: boolean;
  is_converted_to_open: boolean;
  contact_category: string;
  tax_treatment: string;
  tax_treatment_formatted: string;
  contact_persons: string[];
  currency_id: string;
  currency_code: string;
  currency_symbol: string;
  exchange_rate: number;
  expiry_date: string;
  expiry_date_formatted: string;
  discount: number;
  discount_applied_on_amount: number;
  is_discount_before_tax: boolean;
  discount_type: string;
  is_viewed_by_client: boolean;
  client_viewed_time: string;
  client_viewed_time_formatted: string;
  is_inclusive_tax: boolean;
  tax_rounding: string;
  is_digitally_signed: boolean;
  is_edited_after_sign: boolean;
  tds_calculation_type: string;
  estimate_url: string;
  line_items: {
    bcy_rate: number;
    description: string;
    discount: number;
    discount_amount: number;
    discounts: [];
    documents: [];
    header_id: string;
    header_name: string;
    image_document_id: string;
    internal_name: string;
    is_combo_product: boolean;
    item_custom_fields: Record<string, unknown>[];
    item_id: string;
    item_order: number;
    item_total: number;
    line_item_id: string;
    line_item_taxes: Record<string, unknown>[];
    name: string;
    package_details: { length: string; width: string; height: string; weight: string; weight_unit: string };
    pricebook_id: string;
    pricing_scheme: string;
    quantity: number;
    rate: number;
    sku: string;
    tags: string[];
    tax_id: string;
    tax_name: string;
    tax_percentage: number;
    tax_type: string;
    unit: string;
  }[];
  submitter_id: string;
  submitted_date: string;
  submitted_date_formatted: string;
  submitted_by: string;
  submitted_by_name: string;
  submitted_by_email: string;
  submitted_by_photo_url: string;
  approver_id: string;
  shipping_charge_tax_id: string;
  shipping_charge_tax_name: string;
  shipping_charge_tax_type: string;
  shipping_charge_tax_percentage: string;
  shipping_charge_tax: string;
  bcy_shipping_charge_tax: string;
  shipping_charge_exclusive_of_tax: number;
  shipping_charge_inclusive_of_tax: number;
  shipping_charge_tax_formatted: string;
  shipping_charge_exclusive_of_tax_formatted: string;
  shipping_charge_inclusive_of_tax_formatted: string;
  shipping_charge: number;
  shipping_charge_formatted: string;
  bcy_shipping_charge: number;
  adjustment: number;
  adjustment_formatted: string;
  bcy_adjustment: number;
  adjustment_description: string;
  roundoff_value: number;
  roundoff_value_formatted: string;
  transaction_rounding_type: string;
  sub_total: number;
  sub_total_formatted: string;
  bcy_sub_total: number;
  sub_total_inclusive_of_tax: number;
  sub_total_inclusive_of_tax_formatted: string;
  sub_total_exclusive_of_discount: number;
  sub_total_exclusive_of_discount_formatted: string;
  discount_total: number;
  discount_total_formatted: string;
  bcy_discount_total: number;
  discount_percent: number;
  total: number;
  total_formatted: string;
  bcy_total: number;
  tax_total: number;
  tax_total_formatted: string;
  bcy_tax_total: number;
  price_precision: number;
  taxes: [];
  tax_override_preference: string;
  tds_override_preference: string;
  invoice_ids: [];
  subscription_ids: [];
  billing_address: {
    address: string;
    street2: string;
    city: string;
    state: string;
    zip: string;
    country: string;
    fax: string;
    phone: string;
    attention: string;
  };
  shipping_address: {
    address: string;
    street2: string;
    city: string;
    state: string;
    zip: string;
    country: string;
    fax: string;
    phone: string;
    attention: '';
  };
  customer_default_billing_address: Record<string, unknown>;
  notes: string;
  terms: string;
  custom_fields: Record<string, unknown>[];
  custom_field_hash: {
    cf_envoi_devis: string;
    cf_envoi_devis_unformatted: boolean;
    cf_renvoi_devis: string;
    cf_renvoi_devis_unformatted: boolean;
    cf_adresse_compl_te_chantier: string;
    cf_adresse_compl_te_chantier_unformatted: string;
    cf_adresse_du_chantier: string;
    cf_adresse_du_chantier_unformatted: string;
    cf_contact_chantier: string;
    cf_contact_chantier_unformatted: string;
    cf_bdc_client: string;
    cf_bdc_client_unformatted: string;
    cf_encours_apr_s_commande: string;
    cf_encours_apr_s_commande_unformatted: string;
    cf_crm_contact_id: string;
    cf_crm_contact_id_unformatted: string;
    cf_modifi_par: string;
    cf_modifi_par_unformatted: string;
    cf_books_contact_id: string;
    cf_books_contact_id_unformatted: string;
    cf_salesperson_phone: string;
    cf_salesperson_phone_unformatted: string;
    cf_objet: string;
    cf_objet_unformatted: string;
    cf_modifi_par_nom: string;
    cf_modifi_par_nom_unformatted: string;
    cf_modifi_par_telephone: string;
    cf_modifi_par_telephone_unformatted: string;
    cf_modifi_par_email: string;
    cf_modifi_par_email_unformatted: string;
    cf_api_user: string;
    cf_api_user_unformatted: string;
    cf_chantier_longitude: string;
    cf_chantier_longitude_unformatted: string;
    cf_chantier_latitude: string;
    cf_chantier_latitude_unformatted: string;
    cf_gps: string;
    cf_gps_unformatted: string;
    cf_demande_commerciale: string;
    cf_r_ponse_logistique: string;
    cf_retainerinvoice_id: string;
  };
  template_id: string;
  template_name: string;
  template_type: string;
  template_type_formatted: string;
  is_signature_enabled_in_template: boolean;
  page_width: string;
  page_height: string;
  orientation: string;
  created_time: string;
  last_modified_time: string;
  created_by_id: string;
  last_modified_by_id: string;
  salesperson_id: string;
  salesperson_name: string;
  attachment_name: string;
  can_send_in_mail: boolean;
  can_send_estimate_sms: boolean;
  allow_partial_payments: boolean;
  estimate_type: string;
  accept_retainer: boolean;
  retainer_percentage: string;
  retainer_percentage_formatted: string;
  subject_content: string;
};
export type ZohoSalesOrder = {
  salesorder_id: string;
  documents: [];
  zcrm_potential_id: string;
  zcrm_potential_name: string;
  salesorder_number: string;
  date: string;
  date_formatted: string;
  offline_created_date_with_time: string;
  offline_created_date_with_time_formatted: string;
  tracking_url: string;
  has_discount: boolean;
  status: string;
  status_formatted: string;
  color_code: string;
  current_sub_status_id: string;
  current_sub_status: string;
  current_sub_status_formatted: string;
  sub_statuses: [];
  order_sub_status_id: string;
  invoiced_sub_status_id: string;
  shipped_sub_status_id: string;
  order_sub_status: string;
  order_sub_status_formatted: string;
  invoiced_sub_status: string;
  invoiced_sub_status_formatted: string;
  shipped_sub_status: string;
  shipped_sub_status_formatted: string;
  shipment_date: string;
  shipment_date_formatted: string;
  reference_number: string;
  customer_id: string;
  customer_name: string;
  contact_persons: [];
  contact_persons_associated: [];
  contact_person_details: [];
  source: string;
  contact_category: string;
  tax_treatment: string;
  tax_treatment_formatted: string;
  has_shipping_address: boolean;
  currency_id: string;
  currency_code: string;
  currency_symbol: string;
  exchange_rate: number;
  is_discount_before_tax: boolean;
  discount_type: string;
  estimate_id: string;
  delivery_method: string;
  delivery_method_id: string;
  is_inclusive_tax: boolean;
  tax_rounding: string;
  tax_override_preference: string;
  tds_override_preference: string;
  order_status: string;
  order_status_formatted: string;
  invoiced_status: string;
  invoiced_status_formatted: string;
  paid_status: string;
  paid_status_formatted: string;
  shipped_status: string;
  shipped_status_formatted: string;
  sales_channel: string;
  sales_channel_formatted: string;
  account_identifier: string;
  integration_id: string;
  is_dropshipped: boolean;
  is_backordered: boolean;
  is_manually_fulfilled: boolean;
  can_manually_fulfill: boolean;
  has_qty_cancelled: boolean;
  shipping_details: Record<string, unknown>;
  created_by_email: string;
  created_by_name: string;
  total_quantity: number;
  total_quantity_formatted: string;
  line_items: {
    bcy_rate: number;
    description: string;
    discount: number;
    discount_amount: number;
    discounts: [];
    documents: [];
    header_id: string;
    header_name: string;
    image_document_id: string;
    internal_name: string;
    is_combo_product: boolean;
    item_custom_fields: Record<string, unknown>[];
    item_id: string;
    item_order: number;
    item_total: number;
    line_item_id: string;
    line_item_taxes: Record<string, unknown>[];
    name: string;
    package_details: { length: string; width: string; height: string; weight: string; weight_unit: string };
    pricebook_id: string;
    pricing_scheme: string;
    quantity: number;
    rate: number;
    sku: string;
    tags: string[];
    tax_id: string;
    tax_name: string;
    tax_percentage: number;
    tax_type: string;
    unit: string;
  }[];
  entity_tags: string;
  submitter_id: string;
  approver_id: string;
  submitted_date: string;
  submitted_date_formatted: string;
  submitted_by: string;
  submitted_by_name: string;
  submitted_by_email: string;
  submitted_by_photo_url: string;
  order_sub_statuses: string[];
  invoice_sub_statuses: string[];
  shipment_sub_statuses: string[];
  price_precision: number;
  is_emailed: boolean;
  has_unconfirmed_line_item: boolean;
  picklists: string[];
  purchaseorders: string[];
  billing_address: {
    address: string;
    street2: string;
    city: string;
    state: string;
    zip: string;
    country: string;
    fax: string;
    phone: string;
    attention: string;
  };
  shipping_address: {
    address: string;
    street2: string;
    city: string;
    state: string;
    zip: string;
    country: string;
    fax: string;
    phone: string;
    attention: '';
  };
  customer_default_billing_address: Record<string, unknown>;
  custom_fields: Record<string, unknown>[];
  custom_field_hash: {
    cf_envoi_devis: string;
    cf_envoi_devis_unformatted: boolean;
    cf_renvoi_devis: string;
    cf_renvoi_devis_unformatted: boolean;
    cf_adresse_compl_te_chantier: string;
    cf_adresse_compl_te_chantier_unformatted: string;
    cf_adresse_du_chantier: string;
    cf_adresse_du_chantier_unformatted: string;
    cf_contact_chantier: string;
    cf_contact_chantier_unformatted: string;
    cf_bdc_client: string;
    cf_bdc_client_unformatted: string;
    cf_encours_apr_s_commande: string;
    cf_encours_apr_s_commande_unformatted: string;
    cf_crm_contact_id: string;
    cf_crm_contact_id_unformatted: string;
    cf_modifi_par: string;
    cf_modifi_par_unformatted: string;
    cf_books_contact_id: string;
    cf_books_contact_id_unformatted: string;
    cf_salesperson_phone: string;
    cf_salesperson_phone_unformatted: string;
    cf_objet: string;
    cf_objet_unformatted: string;
    cf_modifi_par_nom: string;
    cf_modifi_par_nom_unformatted: string;
    cf_modifi_par_telephone: string;
    cf_modifi_par_telephone_unformatted: string;
    cf_modifi_par_email: string;
    cf_modifi_par_email_unformatted: string;
    cf_api_user: string;
    cf_api_user_unformatted: string;
    cf_chantier_longitude: string;
    cf_chantier_longitude_unformatted: string;
    cf_chantier_latitude: string;
    cf_chantier_latitude_unformatted: string;
    cf_gps: string;
    cf_gps_unformatted: string;
    cf_demande_commerciale: string;
    cf_r_ponse_logistique: string;
    cf_retainerinvoice_id: string;
  };
  billing_address_id: string;
  shipping_address_id: string;
  is_test_order: boolean;
  notes: string;
  terms: string;
  payment_terms: number;
  payment_terms_label: string;
  template_id: string;
  template_name: string;
  page_width: string;
  page_height: string;
  orientation: string;
  template_type: string;
  template_type_formatted: string;
  created_time: string;
  created_time_formatted: string;
  last_modified_time: string;
  last_modified_time_formatted: string;
  created_by_id: string;
  created_date: string;
  created_date_formatted: string;
  last_modified_by_id: string;
  attachment_name: string;
  can_send_in_mail: boolean;
  salesperson_id: string;
  salesperson_name: string;
  merchant_id: string;
  merchant_name: string;
  pickup_location_id: string;
  discount: number;
  discount_applied_on_amount: number;
  is_adv_tracking_in_package: boolean;
  shipping_charge_tax_id: string;
  shipping_charge_tax_name: string;
  shipping_charge_tax_type: string;
  shipping_charge_tax_percentage: string;
  shipping_charge_tax: string;
  bcy_shipping_charge_tax: string;
  shipping_charge_exclusive_of_tax: number;
  shipping_charge_inclusive_of_tax: number;
  shipping_charge_tax_formatted: string;
  shipping_charge_exclusive_of_tax_formatted: string;
  shipping_charge_inclusive_of_tax_formatted: string;
  shipping_charge: number;
  shipping_charge_formatted: string;
  bcy_shipping_charge: number;
  adjustment: number;
  adjustment_formatted: string;
  bcy_adjustment: number;
  adjustment_description: string;
  roundoff_value: number;
  roundoff_value_formatted: string;
  transaction_rounding_type: string;
  sub_total: number;
  sub_total_formatted: string;
  bcy_sub_total: number;
  sub_total_inclusive_of_tax: number;
  sub_total_inclusive_of_tax_formatted: string;
  sub_total_exclusive_of_discount: number;
  sub_total_exclusive_of_discount_formatted: string;
  discount_total: number;
  discount_total_formatted: string;
  bcy_discount_total: number;
  discount_percent: number;
  tax_total: number;
  tax_total_formatted: string;
  bcy_tax_total: number;
  total: number;
  total_formatted: string;
  computation_type: string;
  bcy_total: number;
  taxes: string[];
  tds_calculation_type: string;
  packages: string[];
  so_cycle_preference: Record<string, unknown>;
  invoices: string[];
  salesreturns: string[];
  payments: string[];
  creditnotes: string[];
  refunds: string[];
  contact: Record<string, string>;
  balance: number;
  balance_formatted: string;
  approvers_list: string[];
};

export type FileUploadResponse = {
  document_id: string;
  file_name: string;
  file_type: string;
  file_size: number;
  file_size_formatted: string;
};
