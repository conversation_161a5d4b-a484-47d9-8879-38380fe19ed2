// Simple types based on frontend structure from ../testFront/qbofrontend

export interface Comment {
  id: number;
  createdBy: string;
  createdAt: string;
  comment: string;
}

export interface Avoir {
  id: number;
  avoirNumber: string;
  createdAt: string;
  amount: number;
  createdBy: string;
}

export interface ProviderInvoiceListItem {
  id: number;
  createdAt: string;
  createdBy: string;
  prestataire: string;
  prestataireInvoiceNumber: string;
  status: string;
  totalSelected: number;
  totalAvoirs: number;
  totalAmount: number;
  invoicedAmount: number;
}

// Simple request types
export interface CreateProviderInvoiceRequest {
  prestataire: string;
  prestataireInvoiceNumber?: string;
  status?: string;
  totalAmount?: number;
  totalSelected?: number;
}

export interface UpdateProviderInvoiceRequest {
  prestataireInvoiceNumber?: string;
  status?: string;
  totalAmount?: number;
  totalSelected?: number;
}

