export const FILE_TYPE = {
  PAYMENT: 'PAYMENT',
  BDC_CLIENT: 'BDC_CLIENT',
};

export const DOCUMENT_STATUS = {
  DRAFT: 'draft',
  OPEN: 'open',
  SENT: 'sent',
  ACCEPTED: 'accepted',
  CONFIRMED: 'confirmed',
  TO_PROCESS: 'toProcess',
  PROCESSED: 'processed',
  CONSIDERED: 'considered',
  VOID: 'void',
};

export const BO_LOGISTIC_ACTION = {
  A_TRAITER: 'A_TRAITER',
  PLANIFIEE: 'PLANIFIEE',
  ATTENTE_RETOUR_PRESTA: 'ATTENTE_RETOUR_PRESTA',
  ATTENTE_RETOUR_COMMERCIAL: 'ATTENTE_RETOUR_COMMERCIAL',
};

export const DOCUMENT_TYPES = {
  QUOTATION: 'quotation',
  ORDER: 'order',
  INVOICE: 'invoice',
};

export const formatCoordinate = (value: number) => Number(value.toFixed(6));

export const ZOHO_DOCUMENT_SYNC_STATUS = {
  IN_PROGRESS: 'IN_PROGRESS',
  FAILED: 'FAILED',
  SUCCESS: 'SUCCESS',
};
