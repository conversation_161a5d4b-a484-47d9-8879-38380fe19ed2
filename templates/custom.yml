custom:
  appName: ${self:app}
  serviceName: ${self:service}
  zohoService: qbozohoservice
  userService: qbouserservice
  clientService: qboclientservice
  awsRegion: ${env:REGION}
  baseName: ${self:custom.serviceName}-${self:provider.stage}-${self:custom.awsRegion}
  databaseLayer: ${env:DATABASE_LAYER_ARN}
  middyLayer: ${env:MIDDY_LAYER_ARN}
  utilsLayer: ${env:UTILS_LAYER_ARN}
  authorizerArn: arn:aws:lambda:${self:custom.awsRegion}:${aws:accountId}:function:${self:custom.userService}-${self:provider.stage}-JwtAuthorizer
  zohoResponsesTopic: ${ssm:/qbo/${self:provider.stage}/ZohoResponsesTopic, ""}
  zohoWebhooksTopic: ${ssm:/qbo/${self:provider.stage}/ZohoWebhooksTopic, ""}
  zohoRequestsTopic: ${ssm:/qbo/${self:provider.stage}/ZohoRequestsTopic, ""}
  eventBusName: ${self:custom.zohoService}-${self:provider.stage}-${self:custom.awsRegion}-ZohoEventBus
  stepFunctions:
    CreateContactAddressArn:
      Fn::Sub: arn:aws:lambda:${self:provider.region}:${AWS::AccountId}:function:${self:custom.clientService}-${self:provider.stage}-CreateContactAddress
    RollbackContactAddressArn:
      Fn::Sub: arn:aws:lambda:${self:provider.region}:${AWS::AccountId}:function:${self:custom.clientService}-${self:provider.stage}-RollbackContactAddress
    SyncDocumentToZoho:
      ${self:custom.zohoService}-${self:provider.stage}-SyncDocumentToZoho
    SyncDocumentToZohoArn:
      Fn::Sub: arn:aws:lambda:${self:provider.region}:${AWS::AccountId}:function:${self:custom.zohoService}-${self:provider.stage}-SyncDocumentToZoho
    SyncContactAddressFromZoho:
      ${self:custom.clientService}-${self:provider.stage}-SyncContactAddressFromZoho
    SyncContactAddressFromZohoArn:
      Fn::Sub: arn:aws:lambda:${self:provider.region}:${AWS::AccountId}:function:${self:custom.clientService}-${self:provider.stage}-SyncContactAddressFromZoho
  databaseChangeTopic: ${ssm:/qbo/${self:provider.stage}/DatabaseChangeTopic, ""}

  vpc:
    uat:
      securityGroupIds:
        - ${env:VPC_SECURITY_GROUP_ID}
        - ${env:VPC_LAMBDA_SECURITY_GROUP_ID}
      subnetIds:
        - ${env:VPC_SUBNET_ID_1}
        - ${env:VPC_SUBNET_ID_2}
    prod:
      securityGroupIds:
        - ${env:VPC_SECURITY_GROUP_ID}
        - ${env:VPC_LAMBDA_SECURITY_GROUP_ID}
      subnetIds:
        - ${env:VPC_SUBNET_ID_1}
        - ${env:VPC_SUBNET_ID_2}
        - ${env:VPC_SUBNET_ID_3}
  tags:
    application: ${self:custom.appName}
    service: ${self:custom.serviceName}