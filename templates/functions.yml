DocumentProductLinePrestationSubOption:
  handler: src/handlers/document-product-line-prestation-sub-option-handler.handler
  events:
    - http:
        path: /document-product-line-prestation-sub-options
        method: GET
        cors: true
        authorizer:
          arn: ${self:custom.authorizerArn}
    - http:
        path: /document-product-line-prestation-sub-options/{id}
        method: GET
        cors: true
        authorizer:
          arn: ${self:custom.authorizerArn}
    - http:
        path: /document-product-line-prestation-sub-options
        method: POST
        cors: true
        authorizer:
          arn: ${self:custom.authorizerArn}
    - http:
        path: /document-product-line-prestation-sub-options/{id}
        method: PUT
        cors: true
        authorizer:
          arn: ${self:custom.authorizerArn}
    - http:
        path: /document-product-line-prestation-sub-options/{id}
        method: DELETE
        cors: true
        authorizer:
          arn: ${self:custom.authorizerArn}
DocumentProductLine:
  handler: src/handlers/document-product-lines-handler.handler
  events:
    - http:
        path: /document-product-line
        method: GET
        cors: true
        authorizer:
          arn: ${self:custom.authorizerArn}
    - http:
        path: /document-product-line/{id}
        method: GET
        cors: true
        authorizer:
          arn: ${self:custom.authorizerArn}
    - http:
        path: /document-product-line
        method: POST
        cors: true
        authorizer:
          arn: ${self:custom.authorizerArn}
    - http:
        path: /document-product-line/{id}
        method: PUT
        cors: true
        authorizer:
          arn: ${self:custom.authorizerArn}
    - http:
        path: /document-product-line/{id}
        method: DELETE
        cors: true
        authorizer:
          arn: ${self:custom.authorizerArn}
UploadFile:
    handler: src/handlers/file-handler.handler
    timeout: 29
    events:
      - http:
          path: /upload-file
          method: POST
          cors:
            origin: '*'
            headers:
              - access-control-allow-origin
              - authorization
              - x-requested-with
      - http:
          path: /start-multipart-upload
          method: GET
          cors: true
      - http:
          path: /pre-signed-url-multipart-upload
          method: GET
          cors: true
      - http:
          path: /complete-multipart-upload
          method: POST
          cors: true
      - http:
          path: /pre-signed-url
          method: GET
          cors: true
      - http:
          path: /pre-signed-url-get
          method: GET
          cors: true
UpdateDocumentFromZohoService:
  handler: src/handlers/zoho-webhooks-handler.updateDocumentFromZohoService
  timeout: 119