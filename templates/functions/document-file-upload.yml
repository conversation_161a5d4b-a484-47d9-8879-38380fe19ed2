DocumentFileUpload:
  handler: src/handlers/document-file-upload-handler.handler
  events:
    - http:
        path: /document-file-uploads
        method: GET
        cors: true
        authorizer:
          arn: ${self:custom.authorizerArn}
    - http:
        path: /document-file-uploads/{id}
        method: GET
        cors: true
        authorizer:
          arn: ${self:custom.authorizerArn}
    - http:
        path: /document-file-uploads
        method: POST
        cors: true
        authorizer:
          arn: ${self:custom.authorizerArn}
    - http:
        path: /document-file-uploads/{id}
        method: PUT
        cors: true
        authorizer:
          arn: ${self:custom.authorizerArn}
    - http:
        path: /document-file-uploads/{id}
        method: DELETE
        cors: true
        authorizer:
          arn: ${self:custom.authorizerArn}
  iamRoleStatements:
    - Effect: Allow
      Action:
        - sns:Publish
      Resource: 
        Fn::If:
          - TopicZohoRequestsExists
          - ${self:custom.zohoRequestsTopic}
          - "*"