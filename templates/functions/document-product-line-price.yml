DocumentProductLinePrice:
  handler: src/handlers/document-product-line-price-handler.handler
  events:
    - http:
        path: /document-product-line-price
        method: GET
        cors: true
        authorizer:
          arn: ${self:custom.authorizerArn}
    - http:
        path: /document-product-line-price/{id}
        method: GET
        cors: true
        authorizer:
          arn: ${self:custom.authorizerArn}
    - http:
        path: /document-product-line-price
        method: POST
        cors: true
        authorizer:
          arn: ${self:custom.authorizerArn}
    - http:
        path: /document-product-line-price/{id}
        method: PUT
        cors: true
        authorizer:
          arn: ${self:custom.authorizerArn}
    - http:
        path: /document-product-line-price/{id}
        method: DELETE
        cors: true
        authorizer:
          arn: ${self:custom.authorizerArn}