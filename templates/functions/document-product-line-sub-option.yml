DocumentProductLineSubOption:
  handler: src/handlers/document-product-line-sub-option-handler.handler
  events:
    - http:
        path: /document-product-line-sub-option
        method: GET
        cors: true
        authorizer:
          arn: ${self:custom.authorizerArn}
    - http:
        path: /document-product-line-sub-option/{id}
        method: GET
        cors: true
        authorizer:
          arn: ${self:custom.authorizerArn}
    - http:
        path: /document-product-line-sub-option
        method: POST
        cors: true
        authorizer:
          arn: ${self:custom.authorizerArn}
    - http:
        path: /document-product-line-sub-option/{id}
        method: PUT
        cors: true
        authorizer:
          arn: ${self:custom.authorizerArn}
    - http:
        path: /document-product-line-sub-option/{id}
        method: DELETE
        cors: true
        authorizer:
          arn: ${self:custom.authorizerArn}