Document:
  handler: src/handlers/document-handler.handler
  timeout: 29
  events:
    - http:
        path: /documents
        method: GET
        cors: true
        authorizer:
          arn: ${self:custom.authorizerArn}
    - http:
        path: /document/{id}
        method: ANY
        cors: true
        authorizer:
          arn: ${self:custom.authorizerArn}
    - http:
        path: /document-update-comment/{id}
        method: PUT
        cors: true
        authorizer:
          arn: ${self:custom.authorizerArn}
    - http:
        path: /document
        method: POST
        cors: true
        authorizer:
          arn: ${self:custom.authorizerArn}
    - http:
        path: /create-estimate-and-order
        method: POST
        cors: true
        authorizer:
          arn: ${self:custom.authorizerArn}
    - http:
        path: /document/{id}
        method: PUT
        cors: true
        authorizer:
          arn: ${self:custom.authorizerArn}
    - http:
        path: /document/{id}
        method: DELETE
        cors: true
        authorizer:
          arn: ${self:custom.authorizerArn}
    - http:
        path: /update-contact-document
        method: POST
        cors: true
    - http:
        path: /zoho/quotation-update-log
        method: PUT
        cors: true
        # authorizer:
        #   arn: ${self:custom.authorizerArn}      
    - http:
        path: /document/{id}/status
        method: PUT
        cors: true
        authorizer:
          arn: ${self:custom.authorizerArn}
    - http:
        path: /document/update-country-region
        method: PUT
        cors: true
        # authorizer:
        #   arn: ${self:custom.authorizerArn}
  iamRoleStatements:
    - Effect: Allow
      Action:
        - sns:Publish
      Resource: 
        Fn::If:
          - TopicZohoRequestsExists
          - ${self:custom.zohoRequestsTopic}
          - "*"
# Create or update document and sync to Zoho using Step Functions
CreateOrUpdateDocument:
  handler: src/handlers/document-handler.createOrUpdateDocument
  timeout: 119
  iamRoleStatements:
    - Effect: Allow
      Action:
        - sns:Publish
      Resource: 
        - ${self:custom.databaseChangeTopic}
        - Fn::If:
          - TopicZohoRequestsExists
          - ${self:custom.zohoRequestsTopic}
          - "*"
    - Effect: Allow
      Action:
        - lambda:InvokeFunction
      Resource:
        - ${self:custom.stepFunctions.SyncDocumentToZohoArn}
        - ${self:custom.stepFunctions.SyncContactAddressFromZohoArn}