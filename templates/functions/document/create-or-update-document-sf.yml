name: ${self:custom.baseName}-CreateOrUpdateDocument
type: EXPRESS
loggingConfig:
  level: ALL
  includeExecutionData: true
  destinations:
    - Fn::GetAtt: [CreateDocumentStepFunctionLogs, Arn]
events:
  - http:
      path: /documents
      method: POST
      cors: true
      action: StartSyncExecution
      authorizer:
        arn: ${self:custom.authorizerArn}
        type: request
        resultTtlInSeconds: 0
      # responses:
      #   '200':
      #     statusCode: '200'
      #     responseTemplates:
      #       application/json: '{"output":$input.json(''$.output'')}'
      request:
        template:
          application/json:
            Fn::Sub: |
              {
                "input":"{\"path\": \"$context.path\", \"body\": $util.escapeJavaScript($input.json('$')).replaceAll("\\'","'") }",
                "stateMachineArn": "arn:aws:states:${AWS::Region}:${AWS::AccountId}:stateMachine:${self:custom.baseName}-CreateOrUpdateDocument"
              }
  - http:
      path: /documents/create-estimate-and-order
      method: POST
      cors: true
      action: StartSyncExecution
      authorizer:
        arn: ${self:custom.authorizerArn}
        type: request
        resultTtlInSeconds: 0
      # responses:
      #   '200':
      #     statusCode: '200'
      #     responseTemplates:
      #       application/json: '{"output":$input.json(''$.output'')}'
      request:
        template:
          application/json:
            Fn::Sub: |
              {
                "input":"{\"path\": \"$context.path\", \"body\": $util.escapeJavaScript($input.json('$')).replaceAll("\\'","'") }",
                "stateMachineArn": "arn:aws:states:${AWS::Region}:${AWS::AccountId}:stateMachine:${self:custom.baseName}-CreateOrUpdateDocument"
              }
  - http:
      path: /documents/{id}
      method: PUT
      cors: true
      action: StartSyncExecution
      authorizer:
        arn: ${self:custom.authorizerArn}
        type: request
        resultTtlInSeconds: 0
      # responses:
      #   '200':
      #     statusCode: '200'
      #     responseTemplates:
      #       application/json: '{"output":$input.json(''$.output'')}'
      request:
        template:
          application/json:
            Fn::Sub: |
              {
                "input":"{\"path\": \"$context.path\", \"pathParameters\": {\"id\": \"$input.params('id')\"}, \"body\": $util.escapeJavaScript($input.json('$')).replaceAll("\\'","'") }",
                "stateMachineArn": "arn:aws:states:${AWS::Region}:${AWS::AccountId}:stateMachine:${self:custom.baseName}-CreateOrUpdateDocument"
              }
definition: ${file(templates/functions/document/create-or-update-document.statemachine.json)}