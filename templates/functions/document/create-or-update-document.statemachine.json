{"StartAt": "Create Address", "States": {"Create Address": {"Type": "Task", "Resource": "${self:custom.stepFunctions.CreateContactAddressArn}", "Catch": [{"ErrorEquals": ["States.ALL"], "ResultPath": "$.error", "Next": "FailState"}], "Next": "Create Document and Sync to <PERSON><PERSON><PERSON>"}, "Create Document and Sync to Zoho": {"Type": "Task", "Resource": {"Fn::GetAtt": ["CreateOrUpdateDocument", "<PERSON><PERSON>"]}, "Next": "Create Document Success", "Catch": [{"ErrorEquals": ["States.ALL"], "ResultPath": "$.error", "Next": "Rollback Address"}]}, "Rollback Address": {"Type": "Task", "Resource": "${self:custom.stepFunctions.RollbackContactAddressArn}", "Catch": [{"ErrorEquals": ["States.ALL"], "ResultPath": "$.error", "Next": "FailState"}], "Next": "FailState"}, "FailState": {"Type": "Fail", "CausePath": "$.error.Cause", "ErrorPath": "$.error.Error"}, "Create Document Success": {"Type": "Pass", "End": true}}}