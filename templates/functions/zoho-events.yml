ZohoResponse:
  handler: src/handlers/zoho-response-handler.handler
  events:
    - sqs:
        arn:
          Fn::GetAtt: [ZohoResponsesQueue, Arn]
  iamRoleStatements:
    - Effect: Allow
      Action:
        - sns:Publish
      Resource: 
        Fn::If:
          - TopicZohoRequestsExists
          - ${self:custom.zohoRequestsTopic}
          - "*"

ZohoWebhooks:
  handler: src/handlers/zoho-webhooks-handler.handler
  events:
    - sqs:
        arn:
          Fn::GetAtt: [ZohoWebhooksQueue, Arn]
