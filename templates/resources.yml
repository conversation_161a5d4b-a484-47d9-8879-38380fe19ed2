Conditions:
  TopicZohoResponsesExists:
    Fn::Not:
      - Fn::Equals:
        - ${self:custom.zohoResponsesTopic, ''}
        - ''
  TopicZohoRequestsExists:
    Fn::Not:
      - Fn::Equals:
        - ${self:custom.zohoRequestsTopic, ''}
        - ''
  TopicZohoWebhooksExists:
    Fn::Not:
      - Fn::Equals:
        - ${self:custom.zohoWebhooksTopic, ''}
        - ''
  IsUatOrProd:
    Fn::Or:
      - Fn::Equals:
        - ${self:provider.stage}
        - 'uat'
      - Fn::Equals:
        - ${self:provider.stage}
        - 'prod'
Resources:
###################################
###        ZOHO RESPONSES       ###
###################################
  ZohoResponsesQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:custom.baseName}-ZohoResponsesQueue

  ZohoResponsesSubscriptionDLQ:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:custom.baseName}-ZohoResponsesSubscriptionDLQ

  ZohoResponsesQueuePolicy:
    Type: AWS::SQS::QueuePolicy
    Condition: TopicZohoResponsesExists
    Properties:
      Queues:
        - !Ref ZohoResponsesQueue
        - !Ref ZohoResponsesSubscriptionDLQ
      PolicyDocument:
        Statement:
          Effect: Allow
          Principal: '*'
          Action: sqs:SendMessage
          Resource: '*'
          Condition:
            ArnEquals:
              aws:SourceArn: ${self:custom.zohoResponsesTopic}

  ZohoResponsesQueueToZohoResponsesTopicSubscription:
    Type: AWS::SNS::Subscription
    Condition: TopicZohoResponsesExists
    Properties:
      Endpoint:
        Fn::GetAtt: [ZohoResponsesQueue, Arn]
      Protocol: sqs
      RawMessageDelivery: true
      TopicArn: ${self:custom.zohoResponsesTopic}
      FilterPolicy:
        eventModel:
          - Document
          - DocumentFileUpload
      RedrivePolicy:
        deadLetterTargetArn:
          Fn::GetAtt: [ZohoResponsesSubscriptionDLQ, Arn]

###################################
###        ZOHO WEBHOOKS        ###
###################################
  ZohoWebhooksQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:custom.baseName}-ZohoWebhooksQueue

  ZohoWebhooksSubscriptionDLQ:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:custom.baseName}-ZohoWebhooksSubscriptionDLQ

  ZohoWebhooksQueuePolicy:
    Type: AWS::SQS::QueuePolicy
    Condition: TopicZohoWebhooksExists
    Properties:
      Queues:
        - !Ref ZohoWebhooksQueue
        - !Ref ZohoWebhooksSubscriptionDLQ
      PolicyDocument:
        Statement:
          Effect: Allow
          Principal: '*'
          Action: sqs:SendMessage
          Resource: '*'
          Condition:
            ArnEquals:
              aws:SourceArn: ${self:custom.zohoWebhooksTopic}

  ZohoWebhooksQueueToZohoWebhooksTopicSubscription:
    Type: AWS::SNS::Subscription
    Condition: TopicZohoWebhooksExists
    Properties:
      Endpoint:
        Fn::GetAtt: [ZohoWebhooksQueue, Arn]
      Protocol: sqs
      RawMessageDelivery: true
      TopicArn: ${self:custom.zohoWebhooksTopic}
      FilterPolicy:
        eventModel:
          - Document
          - DocumentFileUpload
      RedrivePolicy:
        deadLetterTargetArn:
          Fn::GetAtt: [ZohoWebhooksSubscriptionDLQ, Arn]
  ###################################
  ###        CREATE DOCUMENT      ###
  ###################################
  CreateDocumentStepFunctionLogs:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: /aws/vendedlogs/states/${file(templates/custom.yml):custom.baseName}-CreateDocumentStepFunctionLogs